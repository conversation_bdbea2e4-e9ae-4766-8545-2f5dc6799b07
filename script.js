class CameraDashboard {
    constructor() {
        this.cameras = [];
        this.gridLayout = '2x2';
        this.refreshInterval = 30;
        this.isFullscreen = false;
        
        this.init();
    }

    init() {
        this.loadSettings();
        this.setupEventListeners();
        this.generateCameraGrid();
        this.setupAutoRefresh();
    }

    setupEventListeners() {
        // Fullscreen toggle
        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Settings modal
        const settingsBtn = document.getElementById('settingsBtn');
        const settingsModal = document.getElementById('settingsModal');
        const closeBtn = settingsModal.querySelector('.close');
        const saveBtn = document.getElementById('saveSettings');

        settingsBtn.addEventListener('click', () => {
            this.openSettings();
        });

        closeBtn.addEventListener('click', () => {
            settingsModal.style.display = 'none';
        });

        saveBtn.addEventListener('click', () => {
            this.saveSettings();
        });

        // Grid layout change
        document.getElementById('gridLayout').addEventListener('change', (e) => {
            this.updateCameraInputs(e.target.value);
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === settingsModal) {
                settingsModal.style.display = 'none';
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F11') {
                e.preventDefault();
                this.toggleFullscreen();
            }
            if (e.key === 'Escape' && this.isFullscreen) {
                this.toggleFullscreen();
            }
        });
    }

    loadSettings() {
        const savedSettings = localStorage.getItem('cameraDashboardSettings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);
            this.cameras = settings.cameras || [];
            this.gridLayout = settings.gridLayout || '2x2';
            this.refreshInterval = settings.refreshInterval || 30;
        } else {
            // Default cameras for demonstration
            this.cameras = this.getDefaultCameras();
        }
    }

    getDefaultCameras() {
        const defaultCameras = [];
        const maxCameras = this.getMaxCamerasForLayout(this.gridLayout);
        
        for (let i = 0; i < maxCameras; i++) {
            defaultCameras.push({
                name: `Camera ${i + 1}`,
                url: '',
                type: 'iframe' // iframe, image, or rtsp
            });
        }
        
        return defaultCameras;
    }

    getMaxCamerasForLayout(layout) {
        const layoutMap = {
            '2x2': 4,
            '3x2': 6,
            '3x3': 9,
            '4x3': 12
        };
        return layoutMap[layout] || 4;
    }

    generateCameraGrid() {
        const grid = document.getElementById('cameraGrid');
        grid.className = `camera-grid layout-${this.gridLayout}`;
        grid.innerHTML = '';

        this.cameras.forEach((camera, index) => {
            const panel = this.createCameraPanel(camera, index);
            grid.appendChild(panel);
        });
    }

    createCameraPanel(camera, index) {
        const panel = document.createElement('div');
        panel.className = 'camera-panel';
        panel.innerHTML = `
            <div class="camera-header">
                <span>${camera.name}</span>
                <div class="camera-status ${camera.url ? 'online' : ''}"></div>
            </div>
            <div class="camera-content">
                ${this.getCameraContent(camera, index)}
            </div>
        `;

        // Add click handler for full-panel view
        panel.addEventListener('dblclick', () => {
            this.showCameraFullscreen(camera, index);
        });

        return panel;
    }

    getCameraContent(camera, index) {
        if (!camera.url) {
            return '<div class="camera-placeholder">No camera configured<br>Double-click to configure</div>';
        }

        switch (camera.type) {
            case 'iframe':
                // Use proxy for iframe to bypass CORS restrictions
                const proxyUrl = `/proxy/${encodeURIComponent(camera.url)}`;
                return `<iframe class="camera-iframe" 
                               src="${proxyUrl}" 
                               loading="lazy"
                               sandbox="allow-same-origin allow-scripts allow-forms"
                               onerror="this.parentElement.innerHTML='<div class=\\"camera-error\\">Failed to load camera feed</div>'"
                               onload="this.parentElement.parentElement.querySelector('.camera-status').classList.add('online')">
                        </iframe>`;
            
            case 'proxy':
                // Direct proxy embedding without iframe
                return `<div class="camera-proxy" data-url="${camera.url}" data-index="${index}">
                          <div class="loading-spinner">Loading camera...</div>
                        </div>`;
            
            case 'image':
                return `<img class="camera-iframe" 
                            src="${camera.url}" 
                            alt="${camera.name}"
                            onerror="this.parentElement.innerHTML='<div class=\\"camera-error\\">Failed to load camera image</div>'"
                            onload="this.parentElement.parentElement.querySelector('.camera-status').classList.add('online')">`;
            
            case 'rtsp':
                return `<video class="camera-iframe" 
                              controls 
                              autoplay 
                              muted
                              onerror="this.parentElement.innerHTML='<div class=\\"camera-error\\">RTSP stream not supported in browser</div>'">
                          <source src="${camera.url}" type="application/x-rtsp">
                          Your browser does not support RTSP streams.
                        </video>`;
            
            default:
                return '<div class="camera-error">Unknown camera type</div>';
        }
    }

    openSettings() {
        document.getElementById('settingsModal').style.display = 'block';
        document.getElementById('gridLayout').value = this.gridLayout;
        document.getElementById('refreshInterval').value = this.refreshInterval;
        this.updateCameraInputs(this.gridLayout);
    }

    updateCameraInputs(layout) {
        const container = document.getElementById('cameraInputs');
        const maxCameras = this.getMaxCamerasForLayout(layout);
        
        // Ensure we have enough camera objects
        while (this.cameras.length < maxCameras) {
            this.cameras.push({
                name: `Camera ${this.cameras.length + 1}`,
                url: '',
                type: 'iframe'
            });
        }

        container.innerHTML = '';
        
        for (let i = 0; i < maxCameras; i++) {
            const camera = this.cameras[i] || { name: `Camera ${i + 1}`, url: '', type: 'iframe' };
            
            const inputGroup = document.createElement('div');
            inputGroup.className = 'camera-input-group';
            inputGroup.innerHTML = `
                <label>Camera ${i + 1} Name:</label>
                <input type="text" data-camera="${i}" data-field="name" value="${camera.name}" placeholder="Camera Name">
                
                <label>Camera ${i + 1} URL:</label>
                <input type="url" data-camera="${i}" data-field="url" value="${camera.url}" placeholder="Camera URL or Stream">
                
                <label>Camera ${i + 1} Type:</label>
                <select data-camera="${i}" data-field="type">
                    <option value="iframe" ${camera.type === 'iframe' ? 'selected' : ''}>Web Page (iframe with proxy)</option>
                    <option value="proxy" ${camera.type === 'proxy' ? 'selected' : ''}>Direct Proxy (for restricted sites)</option>
                    <option value="image" ${camera.type === 'image' ? 'selected' : ''}>Image/MJPEG</option>
                    <option value="rtsp" ${camera.type === 'rtsp' ? 'selected' : ''}>RTSP Stream</option>
                </select>
            `;
            
            container.appendChild(inputGroup);
        }
    }

    saveSettings() {
        // Update grid layout
        this.gridLayout = document.getElementById('gridLayout').value;
        this.refreshInterval = parseInt(document.getElementById('refreshInterval').value);

        // Update camera settings
        const inputs = document.querySelectorAll('#cameraInputs input, #cameraInputs select');
        inputs.forEach(input => {
            const cameraIndex = parseInt(input.dataset.camera);
            const field = input.dataset.field;
            
            if (!this.cameras[cameraIndex]) {
                this.cameras[cameraIndex] = { name: '', url: '', type: 'iframe' };
            }
            
            this.cameras[cameraIndex][field] = input.value;
        });

        // Save to localStorage
        const settings = {
            cameras: this.cameras,
            gridLayout: this.gridLayout,
            refreshInterval: this.refreshInterval
        };
        localStorage.setItem('cameraDashboardSettings', JSON.stringify(settings));

        // Close modal and regenerate grid
        document.getElementById('settingsModal').style.display = 'none';
        this.generateCameraGrid();
        this.setupAutoRefresh();
        
        // Show success message
        this.showNotification('Settings saved successfully!');
    }

    toggleFullscreen() {
        const container = document.querySelector('.dashboard-container');
        
        if (!this.isFullscreen) {
            container.classList.add('fullscreen');
            this.isFullscreen = true;
            document.getElementById('fullscreenBtn').innerHTML = '⛶ Exit Fullscreen';
        } else {
            container.classList.remove('fullscreen');
            this.isFullscreen = false;
            document.getElementById('fullscreenBtn').innerHTML = '⛶ Fullscreen';
        }
    }

    showCameraFullscreen(camera, index) {
        if (!camera.url) {
            this.openSettings();
            return;
        }

        // Create fullscreen overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0,0,0,0.9);
            z-index: 10000;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        `;

        const header = document.createElement('div');
        header.style.cssText = `
            color: white;
            padding: 20px;
            font-size: 24px;
            text-align: center;
        `;
        header.innerHTML = `
            ${camera.name}
            <button style="float: right; background: #f44336; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; margin-left: 20px;">Close</button>
        `;

        const content = document.createElement('div');
        content.style.cssText = `
            width: 90%;
            height: 80%;
            background: #000;
        `;
        content.innerHTML = this.getCameraContent(camera, index).replace('camera-iframe', 'camera-iframe" style="width: 100%; height: 100%;');

        overlay.appendChild(header);
        overlay.appendChild(content);
        document.body.appendChild(overlay);

        // Close handlers
        const closeBtn = header.querySelector('button');
        closeBtn.addEventListener('click', () => {
            document.body.removeChild(overlay);
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                document.body.removeChild(overlay);
            }
        });

        document.addEventListener('keydown', function escHandler(e) {
            if (e.key === 'Escape') {
                document.body.removeChild(overlay);
                document.removeEventListener('keydown', escHandler);
            }
        });
    }

    setupAutoRefresh() {
        // Clear existing interval
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }

        // Set up new refresh interval for image-type cameras
        this.refreshTimer = setInterval(() => {
            this.cameras.forEach((camera, index) => {
                if (camera.type === 'image' && camera.url) {
                    const img = document.querySelector(`[data-camera-index="${index}"] img`);
                    if (img) {
                        // Add timestamp to prevent caching
                        const url = new URL(camera.url);
                        url.searchParams.set('t', Date.now());
                        img.src = url.toString();
                    }
                }
            });
        }, this.refreshInterval * 1000);
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 1001;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 3000);
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new CameraDashboard();
});