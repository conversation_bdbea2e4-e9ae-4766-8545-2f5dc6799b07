# Azure Deployment Guide for Camera Dashboard

## Quick Azure Deployment

### Option 1: Azure CLI Deployment

1. **Install Azure CLI** (if not already installed):
```bash
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
```

2. **Login to Azure**:
```bash
az login
```

3. **Create Resource Group** (if needed):
```bash
az group create --name camera-dashboard-rg --location eastus
```

4. **Create App Service Plan**:
```bash
az appservice plan create --name camera-dashboard-plan --resource-group camera-dashboard-rg --sku B1 --is-linux
```

5. **Create Web App**:
```bash
az webapp create --resource-group camera-dashboard-rg --plan camera-dashboard-plan --name your-camera-dashboard --runtime "PYTHON|3.11"
```

6. **Deploy the code**:
```bash
az webapp deployment source config-local-git --name your-camera-dashboard --resource-group camera-dashboard-rg
git init
git add .
git commit -m "Initial camera dashboard deployment"
git remote add azure https://<deployment-username>@your-camera-dashboard.scm.azurewebsites.net/your-camera-dashboard.git
git push azure main
```

### Option 2: GitHub Actions Deployment (Recommended)

1. **Fork/Push to GitHub repository**

2. **Create Azure Web App** via Azure Portal:
   - Go to Azure Portal
   - Create new "Web App"
   - Choose Python 3.11 runtime
   - Enable GitHub Actions deployment

3. **Configure GitHub Actions**:
   - Azure will automatically create the workflow file
   - Your app will auto-deploy on every push

### Option 3: ZIP Deployment

1. **Create ZIP file**:
```bash
zip -r camera-dashboard.zip . -x "*.git*" "*.pyc" "__pycache__/*"
```

2. **Deploy via Azure CLI**:
```bash
az webapp deployment source config-zip --resource-group camera-dashboard-rg --name your-camera-dashboard --src camera-dashboard.zip
```

## Configuration

### Environment Variables (set in Azure Portal):
- `FLASK_DEBUG`: `False` (for production)
- `SCM_DO_BUILD_DURING_DEPLOYMENT`: `true`

### Custom Domain (Optional):
1. Go to Azure Portal → Your Web App → Custom domains
2. Add your custom domain
3. Configure SSL certificate

## Testing the Deployment

1. **Access your app**: `https://your-camera-dashboard.azurewebsites.net`
2. **Health check**: `https://your-camera-dashboard.azurewebsites.net/health`
3. **Proxy test**: `https://your-camera-dashboard.azurewebsites.net/proxy/https://httpbin.org/get`

## Wyze Camera Setup

1. Open your deployed dashboard
2. Click Settings
3. For each camera:
   - **Name**: Your camera name
   - **URL**: `https://my.wyze.com/live` (or specific camera URL)
   - **Type**: "Web Page (iframe with proxy)"
4. Save settings

The proxy will handle CORS issues and allow Wyze integration.

## Troubleshooting

### Check logs:
```bash
az webapp log tail --name your-camera-dashboard --resource-group camera-dashboard-rg
```

### Common issues:
- **500 errors**: Check application logs
- **Proxy issues**: Verify target URLs are accessible
- **CORS errors**: Ensure proxy is working correctly

## Security Notes

- Dashboard runs on HTTPS automatically
- Proxy requests are logged
- No credentials are stored server-side
- All settings stored in browser localStorage

## Scaling

- **Basic plan**: Good for personal use
- **Standard plan**: Better performance, custom domains
- **Premium plan**: Advanced features, better SLA