#!/usr/bin/env python3
"""
Azure-optimized Security Camera Dashboard for Wyze Cameras
Designed specifically for Azure App Service with minimal dependencies
"""

from flask import Flask, render_template_string, jsonify, request, Response
import os
import json
import time
from datetime import datetime
import logging

# Configure logging for Azure
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Azure App Service configuration
app.config['DEBUG'] = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

# Default camera configuration for your security setup
DEFAULT_CAMERAS = {
    "doorbell": {
        "name": "Doorbell Camera",
        "description": "Front door security camera",
        "url": "",  # Will be configured via settings
        "rtmp_url": "",  # RTMP stream if available
        "type": "wyze",
        "position": 1,
        "active": True,
        "placeholder": "Configure your Wyze doorbell camera URL in settings"
    },
    "front_yard": {
        "name": "Front Yard Camera",
        "description": "Front yard security monitoring",
        "url": "",  # Will be configured via settings
        "rtmp_url": "",  # RTMP stream if available
        "type": "wyze",
        "position": 2,
        "active": True,
        "placeholder": "Configure your Wyze front yard camera URL in settings"
    },
    "driveway": {
        "name": "Driveway Camera",
        "description": "Driveway security monitoring",
        "url": "",  # Will be configured via settings
        "rtmp_url": "",  # RTMP stream if available
        "type": "wyze",
        "position": 3,
        "active": True,
        "placeholder": "Configure your Wyze driveway camera URL in settings"
    }
}

# Default lock configuration for your security setup
DEFAULT_LOCKS = {
    "main_door": {
        "name": "Main Door Lock",
        "description": "Main entrance smart lock",
        "type": "wyze",
        "position": 1,
        "active": True,
        "status": "unknown",  # locked, unlocked, unknown
        "battery_level": None,
        "last_action": None,
        "hubitat_device_id": "",  # Hubitat device ID if using Hubitat integration
        "wyze_device_id": ""  # Wyze device ID
    },
    "back_door": {
        "name": "Back Door Lock",
        "description": "Secondary entrance smart lock",
        "type": "wyze",
        "position": 2,
        "active": True,
        "status": "unknown",  # locked, unlocked, unknown
        "battery_level": None,
        "last_action": None,
        "hubitat_device_id": "",  # Hubitat device ID if using Hubitat integration
        "wyze_device_id": ""  # Wyze device ID
    }
}

@app.route('/')
def dashboard():
    """Main security dashboard page"""
    return render_template_string(DASHBOARD_HTML)

@app.route('/api/cameras')
def get_cameras():
    """API endpoint to get camera configuration"""
    return jsonify({
        "cameras": DEFAULT_CAMERAS,
        "timestamp": datetime.now().isoformat(),
        "status": "online"
    })

@app.route('/api/cameras/<camera_id>/status')
def camera_status(camera_id):
    """Get status of a specific camera"""
    if camera_id not in DEFAULT_CAMERAS:
        return jsonify({"error": "Camera not found"}), 404

    camera = DEFAULT_CAMERAS[camera_id]
    return jsonify({
        "camera_id": camera_id,
        "name": camera["name"],
        "status": "online" if camera["active"] else "offline",
        "last_check": datetime.now().isoformat(),
        "type": camera["type"]
    })

@app.route('/api/dashboard/stats')
def dashboard_stats():
    """Get dashboard statistics"""
    active_cameras = sum(1 for cam in DEFAULT_CAMERAS.values() if cam["active"])
    return jsonify({
        "total_cameras": len(DEFAULT_CAMERAS),
        "active_cameras": active_cameras,
        "system_status": "operational",
        "last_update": datetime.now().isoformat()
    })

@app.route('/api/cameras/<camera_id>/configure', methods=['POST'])
def configure_camera(camera_id):
    """Configure a specific camera URL"""
    if camera_id not in DEFAULT_CAMERAS:
        return jsonify({"error": "Camera not found"}), 404

    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({"error": "URL is required"}), 400

    # Update camera configuration (in production, you'd save this to a database)
    DEFAULT_CAMERAS[camera_id]['url'] = data['url']
    if 'rtmp_url' in data:
        DEFAULT_CAMERAS[camera_id]['rtmp_url'] = data['rtmp_url']

    return jsonify({
        "message": f"Camera {camera_id} configured successfully",
        "camera": DEFAULT_CAMERAS[camera_id]
    })

@app.route('/api/locks')
def get_locks():
    """API endpoint to get lock configuration and status"""
    return jsonify({
        "locks": DEFAULT_LOCKS,
        "timestamp": datetime.now().isoformat(),
        "status": "online"
    })

@app.route('/api/locks/<lock_id>/status')
def lock_status(lock_id):
    """Get status of a specific lock"""
    if lock_id not in DEFAULT_LOCKS:
        return jsonify({"error": "Lock not found"}), 404

    lock = DEFAULT_LOCKS[lock_id]
    return jsonify({
        "lock_id": lock_id,
        "name": lock["name"],
        "status": lock["status"],
        "battery_level": lock["battery_level"],
        "last_action": lock["last_action"],
        "last_check": datetime.now().isoformat(),
        "type": lock["type"]
    })

@app.route('/api/locks/<lock_id>/control', methods=['POST'])
def control_lock(lock_id):
    """Control a specific lock (lock/unlock)"""
    if lock_id not in DEFAULT_LOCKS:
        return jsonify({"error": "Lock not found"}), 404

    data = request.get_json()
    if not data or 'action' not in data:
        return jsonify({"error": "Action is required (lock/unlock)"}), 400

    action = data['action'].lower()
    if action not in ['lock', 'unlock']:
        return jsonify({"error": "Invalid action. Use 'lock' or 'unlock'"}), 400

    # In production, this would integrate with Hubitat or Wyze API
    # For now, we'll simulate the action
    DEFAULT_LOCKS[lock_id]['status'] = 'locked' if action == 'lock' else 'unlocked'
    DEFAULT_LOCKS[lock_id]['last_action'] = f"{action.title()}ed at {datetime.now().strftime('%H:%M:%S')}"

    logger.info(f"Lock {lock_id} {action} command executed")

    return jsonify({
        "message": f"Lock {lock_id} {action} command sent successfully",
        "lock": DEFAULT_LOCKS[lock_id],
        "action": action,
        "timestamp": datetime.now().isoformat()
    })

@app.route('/api/alexa/status')
def alexa_status():
    """Simulate Alexa status check for all locks"""
    status_report = {
        "alexa_response": "Here's your lock status:",
        "locks": {},
        "timestamp": datetime.now().isoformat(),
        "voice_summary": ""
    }

    voice_parts = []
    for lock_id, lock in DEFAULT_LOCKS.items():
        lock_name = lock['name']
        status = lock['status']

        status_report['locks'][lock_id] = {
            "name": lock_name,
            "status": status,
            "voice_friendly": f"The {lock_name.lower()} is {status}"
        }

        voice_parts.append(f"The {lock_name.lower()} is {status}")

    status_report['voice_summary'] = ". ".join(voice_parts) + "."

    return jsonify(status_report)

@app.route('/api/alexa/command', methods=['POST'])
def alexa_command():
    """Simulate processing an Alexa voice command"""
    data = request.get_json()
    if not data or 'command' not in data:
        return jsonify({"error": "Voice command is required"}), 400

    command = data['command'].lower()

    # Simple command parsing (in production, you'd use more sophisticated NLP)
    if "status" in command or "locked" in command:
        # Status check command
        return alexa_status()

    elif "lock" in command:
        # Lock command
        if "main door" in command or "front door" in command:
            lock_id = "main_door"
        elif "back door" in command:
            lock_id = "back_door"
        elif "all" in command:
            # Lock all doors
            for lid in DEFAULT_LOCKS.keys():
                DEFAULT_LOCKS[lid]['status'] = 'locked'
                DEFAULT_LOCKS[lid]['last_action'] = f"Locked via Alexa at {datetime.now().strftime('%H:%M:%S')}"

            return jsonify({
                "alexa_response": "All doors have been locked.",
                "action": "lock_all",
                "timestamp": datetime.now().isoformat()
            })
        else:
            return jsonify({"error": "Could not identify which door to lock"}), 400

        # Lock specific door
        if lock_id in DEFAULT_LOCKS:
            DEFAULT_LOCKS[lock_id]['status'] = 'locked'
            DEFAULT_LOCKS[lock_id]['last_action'] = f"Locked via Alexa at {datetime.now().strftime('%H:%M:%S')}"

            return jsonify({
                "alexa_response": f"The {DEFAULT_LOCKS[lock_id]['name'].lower()} has been locked.",
                "lock_id": lock_id,
                "action": "lock",
                "timestamp": datetime.now().isoformat()
            })

    return jsonify({"error": "Command not recognized"}), 400

@app.route('/api/hubitat/speak', methods=['POST'])
def hubitat_speak():
    """Send text-to-speech command to Hubitat Echo Speaks device"""
    data = request.get_json()
    if not data or 'command' not in data:
        return jsonify({"error": "Command is required"}), 400

    token = data.get('token')
    device_id = data.get('device_id')
    command = data.get('command')

    if not token or not device_id:
        return jsonify({"error": "Hubitat token and device ID are required"}), 400

    try:
        import urllib.request
        import urllib.parse

        # Encode the command for URL
        encoded_command = urllib.parse.quote(command)

        # Hubitat Maker API URL for Echo Speaks
        hubitat_url = f"http://192.168.68.75/apps/api/{token}/devices/{device_id}/speak/{encoded_command}"

        # Make request to Hubitat
        request_obj = urllib.request.Request(hubitat_url)
        response = urllib.request.urlopen(request_obj, timeout=10)

        if response.status == 200:
            logger.info(f"Successfully sent command to Hubitat: {command}")
            return jsonify({
                "success": True,
                "message": f"Command sent to Alexa via Hubitat: {command}",
                "hubitat_response": response.read().decode('utf-8'),
                "timestamp": datetime.now().isoformat()
            })
        else:
            return jsonify({
                "error": f"Hubitat returned status {response.status}"
            }), 500

    except Exception as e:
        logger.error(f"Failed to send command to Hubitat: {str(e)}")
        return jsonify({
            "error": f"Failed to communicate with Hubitat: {str(e)}"
        }), 500

@app.route('/api/hubitat/test', methods=['POST'])
def test_hubitat():
    """Test connection to Hubitat hub"""
    data = request.get_json()
    if not data:
        return jsonify({"error": "Configuration data required"}), 400

    token = data.get('token')
    device_id = data.get('device_id')

    if not token or not device_id:
        return jsonify({"error": "Token and device ID are required"}), 400

    try:
        import urllib.request

        # Test URL - get device info
        test_url = f"http://192.168.68.75/apps/api/{token}/devices/{device_id}"

        request_obj = urllib.request.Request(test_url)
        response = urllib.request.urlopen(request_obj, timeout=5)

        if response.status == 200:
            device_info = response.read().decode('utf-8')
            return jsonify({
                "success": True,
                "message": "Successfully connected to Hubitat",
                "device_info": device_info,
                "timestamp": datetime.now().isoformat()
            })
        else:
            return jsonify({
                "error": f"Hubitat returned status {response.status}"
            }), 500

    except Exception as e:
        logger.error(f"Failed to test Hubitat connection: {str(e)}")
        return jsonify({
            "error": f"Failed to connect to Hubitat: {str(e)}"
        }), 500

# Portal page HTML
PORTAL_HTML = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal View - Wyze Security Dashboard</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-desktop"></i> Portal View</h1>
                <div class="header-controls">
                    <a href="/settings" class="settings-btn">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                    <a href="/" class="settings-btn">
                        <i class="fas fa-arrow-left"></i> Grid View
                    </a>
                </div>
            </div>
        </header>

        <div class="portal-container" style="height: 80vh;">
            <div class="portal-header">
                <h3><i class="fas fa-shield-alt"></i> Wyze Security Portal</h3>
                <div class="portal-status">
                    <span class="status-dot online"></span>
                    <span>Ready</span>
                </div>
            </div>
            <div class="portal-content" id="portalContent">
                <div class="portal-placeholder">
                    <i class="fas fa-globe"></i>
                    <h4>Wyze Portal</h4>
                    <p>Click below to open your Wyze portal in a new window</p>
                    <button onclick="openWyzePortal()" class="config-btn" style="font-size: 1.2rem; padding: 15px 30px;">
                        <i class="fas fa-external-link-alt"></i> Open Wyze Portal
                    </button>
                    <br><br>
                    <small style="opacity: 0.7;">
                        Configure your portal URL in <a href="/settings" style="color: #64B5F6;">Settings</a> first
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openWyzePortal() {
            const portalUrl = localStorage.getItem('wyzePortalUrl');
            if (portalUrl) {
                // Open in new window with specific size
                const newWindow = window.open(portalUrl, 'WyzePortal', 'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no');

                if (newWindow) {
                    // Update the portal content to show it's opened
                    document.getElementById('portalContent').innerHTML = `
                        <div class="portal-placeholder">
                            <i class="fas fa-check-circle" style="color: #4CAF50;"></i>
                            <h4>Portal Opened!</h4>
                            <p>Your Wyze portal is now open in a new window</p>
                            <button onclick="openWyzePortal()" class="config-btn">
                                <i class="fas fa-redo"></i> Open Again
                            </button>
                            <br><br>
                            <small style="opacity: 0.7;">
                                If the window didn't open, check your popup blocker settings
                            </small>
                        </div>
                    `;

                    // Show success notification
                    showNotification('Wyze portal opened in new window!');
                } else {
                    alert('Popup blocked! Please allow popups for this site and try again.');
                }
            } else {
                alert('No portal URL configured. Please go to Settings first.');
                window.location.href = '/settings';
            }
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(76, 175, 80, 0.9);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                z-index: 1002;
                font-weight: 500;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
            `;

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }

        // Check if portal URL is configured on page load
        document.addEventListener('DOMContentLoaded', function() {
            const portalUrl = localStorage.getItem('wyzePortalUrl');
            if (portalUrl) {
                document.getElementById('portalContent').innerHTML = `
                    <div class="portal-placeholder">
                        <i class="fas fa-globe" style="color: #64B5F6;"></i>
                        <h4>Portal Configured</h4>
                        <p>Your Wyze portal URL is ready: <br><small style="opacity: 0.8;">${portalUrl}</small></p>
                        <button onclick="openWyzePortal()" class="config-btn" style="font-size: 1.2rem; padding: 15px 30px;">
                            <i class="fas fa-external-link-alt"></i> Open Wyze Portal
                        </button>
                        <br><br>
                        <a href="/settings" style="color: #64B5F6; text-decoration: none;">
                            <i class="fas fa-edit"></i> Change URL in Settings
                        </a>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
'''

# Settings page HTML
SETTINGS_HTML = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Settings - Wyze Security Dashboard</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-cog"></i> Camera Settings</h1>
                <a href="/" class="settings-btn">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </header>

        <div class="settings-content">
            <div class="settings-section">
                <h2><i class="fas fa-desktop"></i> Wyze Portal Configuration</h2>
                <p>Enter your Wyze portal URL here. The portal will open in a new window when you click "Portal View":</p>
                <div class="form-group">
                    <label for="portalUrl">Wyze Portal URL:</label>
                    <input type="url" id="portalUrl" placeholder="https://my.wyze.com/live or your portal URL">
                    <button onclick="savePortalUrl()" class="save-btn">Save Portal URL</button>
                </div>
                <div class="info-box">
                    <i class="fas fa-info-circle"></i>
                    <p><strong>How it works:</strong> Due to security restrictions, Wyze cannot be embedded directly. Instead, Portal View will open your Wyze portal in a new window/tab where you can view all your cameras.</p>
                </div>
                <div class="info-box" style="background: rgba(255, 193, 7, 0.2); border-color: rgba(255, 193, 7, 0.4);">
                    <i class="fas fa-lightbulb" style="color: #FFC107;"></i>
                    <p><strong>Tip:</strong> After saving your URL, go back to the dashboard, click "Portal View", then click "Open Wyze Portal" to launch your cameras in a new window.</p>
                </div>
            </div>

            <div class="settings-section">
                <h2><i class="fas fa-video"></i> Individual Camera Streams</h2>
                <p>If you have individual stream URLs for each camera, configure them here for Grid View:</p>

                <div class="camera-config">
                    <h3><i class="fas fa-door-open"></i> Doorbell Camera</h3>
                    <div class="form-group">
                        <label for="doorbellUrl">Stream URL:</label>
                        <input type="url" id="doorbellUrl" placeholder="Individual doorbell camera stream URL">
                        <button onclick="saveCameraUrl('doorbell')" class="save-btn">Save</button>
                    </div>
                </div>

                <div class="camera-config">
                    <h3><i class="fas fa-tree"></i> Front Yard Camera</h3>
                    <div class="form-group">
                        <label for="frontYardUrl">Stream URL:</label>
                        <input type="url" id="frontYardUrl" placeholder="Individual front yard camera stream URL">
                        <button onclick="saveCameraUrl('front_yard')" class="save-btn">Save</button>
                    </div>
                </div>

                <div class="camera-config">
                    <h3><i class="fas fa-car"></i> Driveway Camera</h3>
                    <div class="form-group">
                        <label for="drivewayUrl">Stream URL:</label>
                        <input type="url" id="drivewayUrl" placeholder="Individual driveway camera stream URL">
                        <button onclick="saveCameraUrl('driveway')" class="save-btn">Save</button>
                    </div>
                </div>
            </div>

            <div class="settings-section">
                <h2><i class="fas fa-question-circle"></i> How to Get Camera URLs</h2>
                <div class="help-content">
                    <h4>For Wyze Cameras:</h4>
                    <ul>
                        <li><strong>Portal View:</strong> Use your Wyze web portal URL (like https://my.wyze.com/live)</li>
                        <li><strong>Individual Streams:</strong> You may need to enable RTSP in the Wyze app for each camera</li>
                        <li><strong>RTSP Format:</strong> Usually rtsp://username:password@camera-ip:554/live</li>
                    </ul>

                    <h4>Alternative Options:</h4>
                    <ul>
                        <li>Use Wyze Bridge or similar tools to create RTSP streams</li>
                        <li>Check if your cameras support direct HTTP/MJPEG streams</li>
                        <li>Consider using Home Assistant or similar platforms</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function savePortalUrl() {
            const url = document.getElementById('portalUrl').value;
            if (!url) {
                alert('Please enter a portal URL');
                return;
            }

            // Save to localStorage for now (in production, you'd save to server)
            localStorage.setItem('wyzePortalUrl', url);
            alert('Portal URL saved! Go back to dashboard and try Portal View.');
        }

        function saveCameraUrl(cameraId) {
            const inputId = cameraId === 'front_yard' ? 'frontYardUrl' : cameraId + 'Url';
            const url = document.getElementById(inputId).value;

            if (!url) {
                alert('Please enter a camera URL');
                return;
            }

            // Save to localStorage for now
            localStorage.setItem(`camera_${cameraId}_url`, url);
            alert(`${cameraId} camera URL saved!`);
        }

        // Load saved URLs on page load
        document.addEventListener('DOMContentLoaded', function() {
            const portalUrl = localStorage.getItem('wyzePortalUrl');
            if (portalUrl) {
                document.getElementById('portalUrl').value = portalUrl;
            }

            const doorbellUrl = localStorage.getItem('camera_doorbell_url');
            if (doorbellUrl) {
                document.getElementById('doorbellUrl').value = doorbellUrl;
            }

            const frontYardUrl = localStorage.getItem('camera_front_yard_url');
            if (frontYardUrl) {
                document.getElementById('frontYardUrl').value = frontYardUrl;
            }

            const drivewayUrl = localStorage.getItem('camera_driveway_url');
            if (drivewayUrl) {
                document.getElementById('drivewayUrl').value = drivewayUrl;
            }
        });
    </script>
</body>
</html>
'''

@app.route('/portal')
def portal_page():
    """Dedicated portal page"""
    return render_template_string(PORTAL_HTML)

@app.route('/settings')
def settings_page():
    """Camera settings configuration page"""
    return render_template_string(SETTINGS_HTML)

@app.route('/proxy')
def proxy_page():
    """Proxy page to load external URLs without iframe restrictions"""
    url = request.args.get('url')
    if not url:
        return "No URL provided", 400

    proxy_html = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Camera Feed</title>
        <style>
            body {{ margin: 0; padding: 0; }}
            .proxy-container {{
                width: 100vw;
                height: 100vh;
                display: flex;
                flex-direction: column;
            }}
            .proxy-header {{
                background: #1e3c72;
                color: white;
                padding: 10px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }}
            .proxy-content {{
                flex: 1;
                background: #000;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-family: Arial, sans-serif;
            }}
            .back-btn {{
                background: rgba(255,255,255,0.2);
                color: white;
                text-decoration: none;
                padding: 8px 16px;
                border-radius: 4px;
                border: 1px solid rgba(255,255,255,0.3);
            }}
            .back-btn:hover {{
                background: rgba(255,255,255,0.3);
            }}
        </style>
    </head>
    <body>
        <div class="proxy-container">
            <div class="proxy-header">
                <span>Camera Feed Proxy</span>
                <a href="/" class="back-btn">← Back to Dashboard</a>
            </div>
            <div class="proxy-content">
                <div style="text-align: center;">
                    <h2>🔗 External Link</h2>
                    <p>Click the link below to open your Wyze portal in a new tab:</p>
                    <a href="{url}" target="_blank" style="color: #64B5F6; font-size: 1.2rem; text-decoration: none;">
                        Open Wyze Portal →
                    </a>
                    <br><br>
                    <small style="opacity: 0.7;">
                        Note: Wyze prevents embedding for security reasons.<br>
                        We'll open it in a new tab instead.
                    </small>
                </div>
            </div>
        </div>
        <script>
            // Auto-open the link
            setTimeout(() => {{
                window.open('{url}', '_blank');
            }}, 1000);
        </script>
    </body>
    </html>
    '''
    return proxy_html

# Health check for Azure
@app.route('/health')
def health():
    """Azure health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "wyze-security-dashboard",
        "version": "2.0",
        "timestamp": datetime.now().isoformat()
    }), 200

# HTML Template for the dashboard
DASHBOARD_HTML = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wyze Security Dashboard</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-shield-alt"></i> Security Dashboard</h1>
                <div class="header-controls">
                    <a href="/settings" class="settings-btn">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                    <div class="status-indicator">
                        <span class="status-dot online"></span>
                        <span>System Online</span>
                    </div>
                </div>
            </div>
        </header>

        <div class="stats-bar">
            <div class="stat-item">
                <i class="fas fa-video"></i>
                <span class="stat-value">3</span>
                <span class="stat-label">Wyze Cameras</span>
            </div>
            <div class="stat-item">
                <i class="fas fa-lock"></i>
                <span class="stat-value" id="activeLocks">2</span>
                <span class="stat-label">Smart Locks</span>
            </div>
            <div class="stat-item">
                <i class="fas fa-clock"></i>
                <span class="stat-value" id="lastUpdate">--:--</span>
                <span class="stat-label">Last Update</span>
            </div>
            <div class="stat-item">
                <i class="fas fa-wifi"></i>
                <span class="stat-value">Online</span>
                <span class="stat-label">Connection</span>
            </div>
        </div>

        <!-- Wyze Portal Section -->
        <div class="portal-section">
            <h2><i class="fas fa-video"></i> Security Cameras</h2>
            <div class="portal-card">
                <div class="portal-header">
                    <h3><i class="fas fa-shield-alt"></i> Wyze Portal</h3>
                    <div class="portal-status">
                        <span class="status-dot online"></span>
                        <span>Ready</span>
                    </div>
                </div>
                <div class="portal-content-preview">
                    <div class="portal-placeholder">
                        <i class="fas fa-globe" style="color: #64B5F6;"></i>
                        <h4>View All Cameras</h4>
                        <p>Access your doorbell, front yard, and driveway cameras</p>
                        <div class="portal-buttons">
                            <a href="/portal" class="config-btn">
                                <i class="fas fa-desktop"></i> Open Portal View
                            </a>
                            <a href="/settings" class="config-btn-secondary">
                                <i class="fas fa-cog"></i> Configure
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Smart Locks Section -->
        <div class="locks-section">
            <h2><i class="fas fa-lock"></i> Smart Locks</h2>
            <div class="locks-grid">
                <div class="lock-card" data-lock="main_door">
                    <div class="lock-header">
                        <h3><i class="fas fa-door-open"></i> Main Door Lock</h3>
                        <div class="lock-status unknown" id="mainDoorStatus">
                            <i class="fas fa-question-circle"></i>
                        </div>
                    </div>
                    <div class="lock-content">
                        <div class="lock-info">
                            <div class="lock-state" id="mainDoorState">Unknown</div>
                            <div class="lock-battery" id="mainDoorBattery">Battery: --</div>
                            <div class="lock-last-action" id="mainDoorLastAction">No recent activity</div>
                        </div>
                        <div class="lock-controls">
                            <button class="lock-btn lock-btn-lock" onclick="controlLock('main_door', 'lock')">
                                <i class="fas fa-lock"></i> Lock
                            </button>
                            <button class="lock-btn lock-btn-unlock" onclick="controlLock('main_door', 'unlock')">
                                <i class="fas fa-unlock"></i> Unlock
                            </button>
                        </div>
                    </div>
                </div>

                <div class="lock-card" data-lock="back_door">
                    <div class="lock-header">
                        <h3><i class="fas fa-door-closed"></i> Back Door Lock</h3>
                        <div class="lock-status unknown" id="backDoorStatus">
                            <i class="fas fa-question-circle"></i>
                        </div>
                    </div>
                    <div class="lock-content">
                        <div class="lock-info">
                            <div class="lock-state" id="backDoorState">Unknown</div>
                            <div class="lock-battery" id="backDoorBattery">Battery: --</div>
                            <div class="lock-last-action" id="backDoorLastAction">No recent activity</div>
                        </div>
                        <div class="lock-controls">
                            <button class="lock-btn lock-btn-lock" onclick="controlLock('back_door', 'lock')">
                                <i class="fas fa-lock"></i> Lock
                            </button>
                            <button class="lock-btn lock-btn-unlock" onclick="controlLock('back_door', 'unlock')">
                                <i class="fas fa-unlock"></i> Unlock
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alexa Integration Section -->
        <div class="alexa-section">
            <h2><i class="fab fa-amazon"></i> Alexa Voice Control</h2>
            <div class="alexa-card">
                <div class="alexa-header">
                    <h3><i class="fas fa-microphone"></i> Voice Commands</h3>
                    <div class="alexa-status">
                        <span class="status-dot online"></span>
                        <span>Alexa Ready</span>
                    </div>
                </div>
                <div class="alexa-content">
                    <div class="voice-commands">
                        <h4>Try these voice commands:</h4>
                        <div class="command-list">
                            <div class="command-item">
                                <i class="fas fa-microphone-alt"></i>
                                <span>"Alexa, is the main door locked?"</span>
                            </div>
                            <div class="command-item">
                                <i class="fas fa-microphone-alt"></i>
                                <span>"Alexa, is the back door locked?"</span>
                            </div>
                            <div class="command-item">
                                <i class="fas fa-microphone-alt"></i>
                                <span>"Alexa, lock the main door"</span>
                            </div>
                            <div class="command-item">
                                <i class="fas fa-microphone-alt"></i>
                                <span>"Alexa, lock the back door"</span>
                            </div>
                            <div class="command-item">
                                <i class="fas fa-microphone-alt"></i>
                                <span>"Alexa, lock all doors"</span>
                            </div>
                        </div>
                    </div>
                    <div class="alexa-controls">
                        <button class="alexa-btn" onclick="simulateAlexaCheck()">
                            <i class="fab fa-amazon"></i> Simulate Alexa Status Check
                        </button>
                        <button class="alexa-btn alexa-btn-secondary" onclick="showAlexaHelp()">
                            <i class="fas fa-question-circle"></i> Voice Setup Help
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hubitat Echo Speaks Integration -->
        <div class="hubitat-section">
            <h2><i class="fas fa-home"></i> Hubitat Echo Speaks</h2>
            <div class="hubitat-card">
                <div class="hubitat-header">
                    <h3><i class="fas fa-microphone-slash"></i> Text-to-Voice Commands</h3>
                    <div class="hubitat-status">
                        <span class="status-dot unknown" id="hubitatStatus"></span>
                        <span>Hubitat C8 (192.168.68.75)</span>
                    </div>
                </div>
                <div class="hubitat-content">
                    <div class="hubitat-config">
                        <h4>Hubitat Configuration:</h4>
                        <div class="config-row">
                            <label for="hubitatToken">Maker API Access Token:</label>
                            <input type="password" id="hubitatToken" placeholder="Your Maker API token from Hubitat" value="">
                            <small>Get this from Apps → Maker API in your Hubitat interface</small>
                        </div>
                        <div class="config-row">
                            <label for="echoDeviceId">Echo Device ID:</label>
                            <input type="text" id="echoDeviceId" placeholder="Echo device ID (e.g., 123)" value="">
                            <small>Find this in your Hubitat device list</small>
                        </div>
                        <div class="config-buttons">
                            <button class="hubitat-btn" onclick="saveHubitatConfig()">
                                <i class="fas fa-save"></i> Save Configuration
                            </button>
                            <button class="hubitat-btn hubitat-btn-test" onclick="testHubitatConnection()">
                                <i class="fas fa-plug"></i> Test Connection
                            </button>
                        </div>
                    </div>

                    <div class="voice-commands-hubitat">
                        <h4>Send Voice Commands via Hubitat Echo Speaks:</h4>
                        <div class="command-buttons">
                            <button class="voice-cmd-btn" onclick="sendHubitatCommand('Alexa, is the main door locked?')">
                                <i class="fas fa-door-open"></i> Check Main Door
                            </button>
                            <button class="voice-cmd-btn" onclick="sendHubitatCommand('Alexa, is the back door locked?')">
                                <i class="fas fa-door-closed"></i> Check Back Door
                            </button>
                            <button class="voice-cmd-btn" onclick="sendHubitatCommand('Alexa, lock the main door')">
                                <i class="fas fa-lock"></i> Lock Main Door
                            </button>
                            <button class="voice-cmd-btn" onclick="sendHubitatCommand('Alexa, lock the back door')">
                                <i class="fas fa-lock"></i> Lock Back Door
                            </button>
                            <button class="voice-cmd-btn voice-cmd-emergency" onclick="sendHubitatCommand('Alexa, lock all doors')">
                                <i class="fas fa-shield-alt"></i> Lock All Doors
                            </button>
                        </div>

                        <div class="custom-command">
                            <h4>Custom Voice Command:</h4>
                            <div class="custom-input">
                                <input type="text" id="customCommand" placeholder="Type any Alexa command..." value="">
                                <button class="hubitat-btn" onclick="sendCustomCommand()">
                                    <i class="fas fa-paper-plane"></i> Send to Alexa
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hubitat Echo Speaks Integration -->
        <div class="hubitat-section">
            <h2><i class="fas fa-home"></i> Hubitat Echo Speaks</h2>
            <div class="hubitat-card">
                <div class="hubitat-header">
                    <h3><i class="fas fa-microphone-slash"></i> Text-to-Voice Commands</h3>
                    <div class="hubitat-status">
                        <span class="status-dot" id="hubitatStatus">unknown</span>
                        <span>Hubitat C8</span>
                    </div>
                </div>
                <div class="hubitat-content">
                    <div class="hubitat-config">
                        <h4>Hubitat Configuration:</h4>
                        <div class="config-row">
                            <label for="hubitatIP">Hubitat Hub IP:</label>
                            <input type="text" id="hubitatIP" placeholder="*************" value="">
                        </div>
                        <div class="config-row">
                            <label for="hubitatToken">Access Token:</label>
                            <input type="password" id="hubitatToken" placeholder="Your Maker API token" value="">
                        </div>
                        <div class="config-row">
                            <label for="echoDeviceId">Echo Device ID:</label>
                            <input type="text" id="echoDeviceId" placeholder="Echo device ID in Hubitat" value="">
                        </div>
                        <button class="hubitat-btn" onclick="saveHubitatConfig()">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                        <button class="hubitat-btn hubitat-btn-test" onclick="testHubitatConnection()">
                            <i class="fas fa-plug"></i> Test Connection
                        </button>
                    </div>

                    <div class="voice-commands-hubitat">
                        <h4>Send Voice Commands via Hubitat:</h4>
                        <div class="command-buttons">
                            <button class="voice-cmd-btn" onclick="sendHubitatCommand('Alexa, is the main door locked?')">
                                <i class="fas fa-door-open"></i> Check Main Door
                            </button>
                            <button class="voice-cmd-btn" onclick="sendHubitatCommand('Alexa, is the back door locked?')">
                                <i class="fas fa-door-closed"></i> Check Back Door
                            </button>
                            <button class="voice-cmd-btn" onclick="sendHubitatCommand('Alexa, lock the main door')">
                                <i class="fas fa-lock"></i> Lock Main Door
                            </button>
                            <button class="voice-cmd-btn" onclick="sendHubitatCommand('Alexa, lock the back door')">
                                <i class="fas fa-lock"></i> Lock Back Door
                            </button>
                            <button class="voice-cmd-btn voice-cmd-emergency" onclick="sendHubitatCommand('Alexa, lock all doors')">
                                <i class="fas fa-shield-alt"></i> Lock All Doors
                            </button>
                        </div>

                        <div class="custom-command">
                            <h4>Custom Voice Command:</h4>
                            <div class="custom-input">
                                <input type="text" id="customCommand" placeholder="Type any Alexa command..." value="">
                                <button class="hubitat-btn" onclick="sendCustomCommand()">
                                    <i class="fas fa-paper-plane"></i> Send
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="controls-panel">
            <button class="control-btn" onclick="refreshAll()">
                <i class="fas fa-sync-alt"></i> Refresh All
            </button>
            <button class="control-btn" onclick="lockAll()">
                <i class="fas fa-shield-alt"></i> Lock All
            </button>
            <button class="control-btn" onclick="toggleFullscreen()">
                <i class="fas fa-expand-arrows-alt"></i> Fullscreen
            </button>
        </div>
    </div>

    <!-- Fullscreen Modal -->
    <div id="fullscreenModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeFullscreen()">&times;</span>
            <div id="fullscreenContent"></div>
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
'''

# CSS Styles
DASHBOARD_CSS = '''
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
    color: #fff;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.settings-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 20px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 300;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: #4CAF50;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.stats-bar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-item i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #64B5F6;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.view-toggle {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
}

.toggle-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.toggle-btn.active {
    background: #2196F3;
    border-color: #2196F3;
}

.portal-view {
    margin-bottom: 30px;
}

.portal-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: 600px;
}

.portal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
}

.portal-header h3 {
    font-size: 1.5rem;
    font-weight: 500;
}

.portal-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.portal-content {
    height: calc(100% - 80px);
    background: #000;
    position: relative;
}

.portal-content iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.portal-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    padding: 40px;
}

.portal-placeholder i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.5);
}

.portal-placeholder h4 {
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.portal-placeholder p {
    margin-bottom: 25px;
    font-size: 1rem;
    opacity: 0.8;
    max-width: 400px;
}

/* Portal Section Styles */
.portal-section {
    margin: 40px 0;
}

.portal-section h2 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 2rem;
    color: #64B5F6;
}

.portal-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    max-width: 600px;
    margin: 0 auto;
}

.portal-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.portal-content-preview {
    padding: 40px;
    text-align: center;
}

.portal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.config-btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    padding: 12px 25px;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.config-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.camera-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.camera-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.camera-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.camera-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.2);
}

.camera-header h3 {
    font-size: 1.3rem;
    font-weight: 500;
}

.camera-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

.camera-content {
    position: relative;
    height: 300px;
    background: #000;
}

.camera-content iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.camera-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    padding: 20px;
}

.camera-placeholder i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.5);
}

.camera-placeholder h4 {
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.camera-placeholder p {
    margin-bottom: 20px;
    font-size: 0.9rem;
    opacity: 0.8;
}

.config-btn {
    background: #2196F3;
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.config-btn:hover {
    background: #1976D2;
    transform: translateY(-2px);
}

.camera-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.camera-card:hover .camera-overlay {
    opacity: 1;
}

.fullscreen-btn {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: background 0.3s ease;
}

.fullscreen-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

.camera-footer {
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.1);
    text-align: center;
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Smart Locks Styles */
.locks-section {
    margin: 40px 0;
}

.locks-section h2 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 2rem;
    color: #FFB74D;
}

.locks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.lock-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.lock-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.lock-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
}

.lock-header h3 {
    font-size: 1.3rem;
    font-weight: 500;
}

.lock-status {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.lock-status.locked {
    background: #4CAF50;
    color: white;
}

.lock-status.unlocked {
    background: #FF9800;
    color: white;
}

.lock-status.unknown {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.7);
}

.lock-content {
    padding: 20px;
}

.lock-info {
    margin-bottom: 20px;
}

.lock-state {
    font-size: 1.4rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
}

.lock-battery {
    font-size: 0.9rem;
    opacity: 0.8;
    text-align: center;
    margin-bottom: 5px;
}

.lock-last-action {
    font-size: 0.8rem;
    opacity: 0.7;
    text-align: center;
    font-style: italic;
}

.lock-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.lock-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.lock-btn-lock {
    background: #4CAF50;
    color: white;
}

.lock-btn-lock:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.lock-btn-unlock {
    background: #FF9800;
    color: white;
}

.lock-btn-unlock:hover {
    background: #f57c00;
    transform: translateY(-2px);
}

.lock-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Alexa Integration Styles */
.alexa-section {
    margin: 40px 0;
}

.alexa-section h2 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 2rem;
    color: #00D4FF;
}

.alexa-card {
    background: rgba(0, 212, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(0, 212, 255, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    max-width: 800px;
    margin: 0 auto;
}

.alexa-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 212, 255, 0.2);
}

.alexa-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(0, 212, 255, 0.2);
}

.alexa-header h3 {
    font-size: 1.3rem;
    font-weight: 500;
    color: #00D4FF;
}

.alexa-content {
    padding: 30px;
}

.voice-commands h4 {
    margin-bottom: 20px;
    color: #00D4FF;
    text-align: center;
}

.command-list {
    display: grid;
    gap: 15px;
    margin-bottom: 30px;
}

.command-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(0, 212, 255, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(0, 212, 255, 0.2);
    transition: all 0.3s ease;
}

.command-item:hover {
    background: rgba(0, 212, 255, 0.2);
    transform: translateX(5px);
}

.command-item i {
    color: #00D4FF;
    font-size: 1.1rem;
}

.command-item span {
    font-style: italic;
    color: rgba(255, 255, 255, 0.9);
}

.alexa-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.alexa-btn {
    background: #00D4FF;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alexa-btn:hover {
    background: #00B8E6;
    transform: translateY(-2px);
}

.alexa-btn-secondary {
    background: rgba(0, 212, 255, 0.3);
    border: 1px solid rgba(0, 212, 255, 0.5);
}

.alexa-btn-secondary:hover {
    background: rgba(0, 212, 255, 0.4);
}

/* Hubitat Echo Speaks Styles */
.hubitat-section {
    margin: 40px 0;
}

.hubitat-section h2 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 2rem;
    color: #4CAF50;
}

.hubitat-card {
    background: rgba(76, 175, 80, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(76, 175, 80, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    max-width: 900px;
    margin: 0 auto;
}

.hubitat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(76, 175, 80, 0.2);
}

.hubitat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(76, 175, 80, 0.2);
}

.hubitat-header h3 {
    font-size: 1.3rem;
    font-weight: 500;
    color: #4CAF50;
}

.hubitat-content {
    padding: 30px;
}

.hubitat-config {
    background: rgba(0, 0, 0, 0.2);
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.hubitat-config h4 {
    color: #4CAF50;
    margin-bottom: 20px;
    text-align: center;
}

.config-row {
    margin-bottom: 20px;
}

.config-row label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #81C784;
}

.config-row input {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    margin-bottom: 5px;
}

.config-row input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.config-row small {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
}

.config-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.hubitat-btn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.hubitat-btn:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.hubitat-btn-test {
    background: #FF9800;
}

.hubitat-btn-test:hover {
    background: #f57c00;
}

.voice-commands-hubitat h4 {
    color: #4CAF50;
    margin-bottom: 20px;
    text-align: center;
}

.command-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.voice-cmd-btn {
    background: rgba(76, 175, 80, 0.2);
    color: white;
    border: 1px solid rgba(76, 175, 80, 0.4);
    padding: 15px 20px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-align: center;
}

.voice-cmd-btn:hover {
    background: rgba(76, 175, 80, 0.3);
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(76, 175, 80, 0.2);
}

.voice-cmd-emergency {
    background: rgba(244, 67, 54, 0.2);
    border-color: rgba(244, 67, 54, 0.4);
    color: #FFB3B3;
}

.voice-cmd-emergency:hover {
    background: rgba(244, 67, 54, 0.3);
    box-shadow: 0 8px 16px rgba(244, 67, 54, 0.2);
}

.custom-command {
    background: rgba(0, 0, 0, 0.2);
    padding: 20px;
    border-radius: 15px;
}

.custom-command h4 {
    color: #4CAF50;
    margin-bottom: 15px;
    text-align: center;
}

.custom-input {
    display: flex;
    gap: 10px;
    align-items: center;
}

.custom-input input {
    flex: 1;
    padding: 12px;
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
}

.custom-input input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.controls-panel {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
}

.modal-content {
    position: relative;
    margin: 2% auto;
    width: 95%;
    height: 90%;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
}

.close {
    position: absolute;
    top: 15px;
    right: 25px;
    color: #fff;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
}

.close:hover {
    color: #f44336;
}

#fullscreenContent {
    width: 100%;
    height: 100%;
}

#fullscreenContent iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Settings Page Styles */
.settings-content {
    max-width: 800px;
    margin: 0 auto;
}

.settings-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-section h2 {
    margin-bottom: 15px;
    font-size: 1.5rem;
    color: #64B5F6;
}

.settings-section p {
    margin-bottom: 20px;
    opacity: 0.9;
    line-height: 1.6;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    margin-bottom: 10px;
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.save-btn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.save-btn:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.camera-config {
    background: rgba(0, 0, 0, 0.2);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.camera-config h3 {
    margin-bottom: 15px;
    color: #81C784;
}

.info-box {
    background: rgba(33, 150, 243, 0.2);
    border: 1px solid rgba(33, 150, 243, 0.4);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.info-box i {
    color: #2196F3;
    font-size: 1.2rem;
    margin-top: 2px;
}

.help-content h4 {
    color: #FFB74D;
    margin: 20px 0 10px 0;
}

.help-content ul {
    margin-left: 20px;
    margin-bottom: 20px;
}

.help-content li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .camera-grid {
        grid-template-columns: 1fr;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-content h1 {
        font-size: 2rem;
    }

    .controls-panel {
        flex-direction: column;
        align-items: center;
    }

    .view-toggle {
        flex-direction: column;
        align-items: center;
    }

    .settings-section {
        padding: 20px;
    }
}
'''

# JavaScript for dashboard functionality
DASHBOARD_JS = '''
class SecurityDashboard {
    constructor() {
        this.cameras = {};
        this.updateInterval = null;
        this.init();
    }

    init() {
        this.loadCameraData();
        this.loadLockData();
        this.startAutoUpdate();
        this.updateTimestamp();

        // Update timestamp every minute
        setInterval(() => this.updateTimestamp(), 60000);
    }

    async loadLockData() {
        try {
            const response = await fetch('/api/locks');
            const data = await response.json();
            this.locks = data.locks;
            this.updateLockDisplay();
        } catch (error) {
            console.error('Failed to load lock data:', error);
        }
    }

    updateLockDisplay() {
        Object.keys(this.locks).forEach(lockId => {
            const lock = this.locks[lockId];
            const statusElement = document.getElementById(`${this.camelCase(lockId)}Status`);
            const stateElement = document.getElementById(`${this.camelCase(lockId)}State`);
            const batteryElement = document.getElementById(`${this.camelCase(lockId)}Battery`);
            const actionElement = document.getElementById(`${this.camelCase(lockId)}LastAction`);

            if (statusElement) {
                statusElement.className = `lock-status ${lock.status}`;
                statusElement.innerHTML = lock.status === 'locked' ? '<i class="fas fa-lock"></i>' :
                                        lock.status === 'unlocked' ? '<i class="fas fa-unlock"></i>' :
                                        '<i class="fas fa-question-circle"></i>';
            }

            if (stateElement) {
                stateElement.textContent = lock.status.charAt(0).toUpperCase() + lock.status.slice(1);
            }

            if (batteryElement && lock.battery_level) {
                batteryElement.textContent = `Battery: ${lock.battery_level}%`;
            }

            if (actionElement && lock.last_action) {
                actionElement.textContent = lock.last_action;
            }
        });
    }

    camelCase(str) {
        return str.replace(/_([a-z])/g, (g) => g[1].toUpperCase());
    }

    async loadCameraData() {
        try {
            const response = await fetch('/api/cameras');
            const data = await response.json();
            this.cameras = data.cameras;
            this.updateStats();
        } catch (error) {
            console.error('Failed to load camera data:', error);
        }
    }

    updateStats() {
        const activeCameras = Object.values(this.cameras).filter(cam => cam.active).length;
        document.getElementById('activeCameras').textContent = activeCameras;
    }

    updateTimestamp() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
        document.getElementById('lastUpdate').textContent = timeString;
    }

    startAutoUpdate() {
        // Check camera status every 30 seconds
        this.updateInterval = setInterval(async () => {
            await this.checkCameraStatus();
        }, 30000);
    }

    async checkCameraStatus() {
        for (const cameraId of Object.keys(this.cameras)) {
            try {
                const response = await fetch(`/api/cameras/${cameraId}/status`);
                const status = await response.json();
                this.updateCameraStatus(cameraId, status.status);
            } catch (error) {
                console.error(`Failed to check status for ${cameraId}:`, error);
                this.updateCameraStatus(cameraId, 'offline');
            }
        }
    }

    updateCameraStatus(cameraId, status) {
        const cameraCard = document.querySelector(`[data-camera="${cameraId}"]`);
        if (cameraCard) {
            const statusIndicator = cameraCard.querySelector('.camera-status');
            statusIndicator.className = `camera-status ${status}`;
        }
    }
}

// Global functions for UI interactions
function showGridView() {
    console.log('Switching to Grid View');
    const gridView = document.getElementById('gridView');
    const portalView = document.getElementById('portalView');

    if (gridView) gridView.style.display = 'grid';
    if (portalView) portalView.style.display = 'none';

    // Update toggle buttons
    document.querySelectorAll('.toggle-btn').forEach(btn => btn.classList.remove('active'));
    const gridBtn = document.querySelector('.toggle-btn');
    if (gridBtn) gridBtn.classList.add('active');

    showNotification('Switched to Grid View');
}

function showPortalView() {
    console.log('Switching to Portal View');
    const gridView = document.getElementById('gridView');
    const portalView = document.getElementById('portalView');

    if (gridView) gridView.style.display = 'none';
    if (portalView) portalView.style.display = 'block';

    // Update toggle buttons
    document.querySelectorAll('.toggle-btn').forEach(btn => btn.classList.remove('active'));
    const portalBtn = document.querySelectorAll('.toggle-btn')[1];
    if (portalBtn) portalBtn.classList.add('active');

    showNotification('Switched to Portal View');
}

function openWyzePortal() {
    const portalUrl = localStorage.getItem('wyzePortalUrl');
    if (portalUrl) {
        window.open(portalUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        showNotification('Opening Wyze portal in new window...');
    } else {
        alert('No portal URL configured. Please go to Settings first.');
    }
}

function openFullscreen(cameraId) {
    const modal = document.getElementById('fullscreenModal');
    const content = document.getElementById('fullscreenContent');

    // Get the camera iframe source
    const cameraCard = document.querySelector(`[data-camera="${cameraId}"]`);
    const iframe = cameraCard.querySelector('iframe');

    content.innerHTML = `
        <iframe src="${iframe.src}"
                frameborder="0"
                allowfullscreen
                sandbox="allow-same-origin allow-scripts allow-forms">
        </iframe>
    `;

    modal.style.display = 'block';

    // Close on Escape key
    document.addEventListener('keydown', function escHandler(e) {
        if (e.key === 'Escape') {
            closeFullscreen();
            document.removeEventListener('keydown', escHandler);
        }
    });
}

function closeFullscreen() {
    const modal = document.getElementById('fullscreenModal');
    modal.style.display = 'none';
}

async function controlLock(lockId, action) {
    try {
        // Disable buttons during request
        const lockCard = document.querySelector(`[data-lock="${lockId}"]`);
        const buttons = lockCard.querySelectorAll('.lock-btn');
        buttons.forEach(btn => btn.disabled = true);

        const response = await fetch(`/api/locks/${lockId}/control`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action: action })
        });

        const result = await response.json();

        if (response.ok) {
            // Update lock display
            const statusElement = document.getElementById(`${camelCase(lockId)}Status`);
            const stateElement = document.getElementById(`${camelCase(lockId)}State`);
            const actionElement = document.getElementById(`${camelCase(lockId)}LastAction`);

            if (statusElement) {
                statusElement.className = `lock-status ${result.lock.status}`;
                statusElement.innerHTML = result.lock.status === 'locked' ? '<i class="fas fa-lock"></i>' : '<i class="fas fa-unlock"></i>';
            }

            if (stateElement) {
                stateElement.textContent = result.lock.status.charAt(0).toUpperCase() + result.lock.status.slice(1);
            }

            if (actionElement) {
                actionElement.textContent = result.lock.last_action;
            }

            showNotification(`${result.lock.name} ${action}ed successfully!`);
        } else {
            showNotification(`Failed to ${action} lock: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Lock control error:', error);
        showNotification(`Error controlling lock: ${error.message}`, 'error');
    } finally {
        // Re-enable buttons
        const lockCard = document.querySelector(`[data-lock="${lockId}"]`);
        const buttons = lockCard.querySelectorAll('.lock-btn');
        buttons.forEach(btn => btn.disabled = false);
    }
}

async function lockAll() {
    const locks = ['main_door', 'back_door'];
    let successCount = 0;

    for (const lockId of locks) {
        try {
            const response = await fetch(`/api/locks/${lockId}/control`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ action: 'lock' })
            });

            if (response.ok) {
                successCount++;
            }
        } catch (error) {
            console.error(`Failed to lock ${lockId}:`, error);
        }
    }

    showNotification(`${successCount} of ${locks.length} locks secured`);

    // Refresh lock status
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

function camelCase(str) {
    return str.replace(/_([a-z])/g, (g) => g[1].toUpperCase());
}

async function simulateAlexaCheck() {
    showNotification('Simulating Alexa status check...', 'info');

    // Simulate checking lock status via Alexa
    try {
        const locks = ['main_door', 'back_door'];
        let statusMessage = 'Alexa Status Report:\n';

        for (const lockId of locks) {
            const response = await fetch(`/api/locks/${lockId}/status`);
            const lockData = await response.json();

            const lockName = lockData.name;
            const status = lockData.status;

            statusMessage += `${lockName}: ${status.charAt(0).toUpperCase() + status.slice(1)}\n`;
        }

        // Show status in a more prominent way
        setTimeout(() => {
            alert(statusMessage);
        }, 1000);

    } catch (error) {
        showNotification('Failed to get lock status', 'error');
    }
}

function showAlexaHelp() {
    const helpModal = document.createElement('div');
    helpModal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    helpModal.innerHTML = `
        <div style="
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            color: white;
            position: relative;
            border: 1px solid rgba(0, 212, 255, 0.3);
        ">
            <button onclick="this.parentElement.parentElement.remove()" style="
                position: absolute;
                top: 15px;
                right: 20px;
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
            ">&times;</button>

            <h2 style="color: #00D4FF; margin-bottom: 20px;">
                <i class="fab fa-amazon"></i> Alexa Setup Guide
            </h2>

            <h3 style="color: #64B5F6; margin-bottom: 15px;">Current Alexa Integration:</h3>
            <ul style="margin-bottom: 25px; line-height: 1.6;">
                <li>✅ Your Wyze locks are already connected to Alexa</li>
                <li>✅ Voice commands work through your existing setup</li>
                <li>✅ Status checking is available via voice</li>
            </ul>

            <h3 style="color: #64B5F6; margin-bottom: 15px;">Voice Commands You Can Use:</h3>
            <ul style="margin-bottom: 25px; line-height: 1.6;">
                <li><strong>"Alexa, is the main door locked?"</strong></li>
                <li><strong>"Alexa, is the back door locked?"</strong></li>
                <li><strong>"Alexa, lock the main door"</strong></li>
                <li><strong>"Alexa, lock the back door"</strong></li>
                <li><strong>"Alexa, unlock the main door"</strong></li>
                <li><strong>"Alexa, unlock the back door"</strong></li>
            </ul>

            <h3 style="color: #64B5F6; margin-bottom: 15px;">Advanced Integration Options:</h3>
            <ul style="margin-bottom: 25px; line-height: 1.6;">
                <li>🔄 <strong>Hubitat Integration:</strong> Connect via your C7/C8 hubs for real-time status</li>
                <li>🌐 <strong>Wyze API:</strong> Direct integration with Wyze services</li>
                <li>📱 <strong>Smart Routines:</strong> Create Alexa routines for complex actions</li>
                <li>🏠 <strong>Home Assistant:</strong> Centralized smart home control</li>
            </ul>

            <div style="text-align: center;">
                <button onclick="this.parentElement.parentElement.parentElement.remove()" style="
                    background: #00D4FF;
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 20px;
                    cursor: pointer;
                    font-size: 1rem;
                ">Got It!</button>
            </div>
        </div>
    `;

    document.body.appendChild(helpModal);
}

// Hubitat Echo Speaks Integration Functions
function saveHubitatConfig() {
    const token = document.getElementById('hubitatToken').value;
    const deviceId = document.getElementById('echoDeviceId').value;

    if (!token || !deviceId) {
        showNotification('Please enter both Maker API token and Echo device ID', 'error');
        return;
    }

    // Save to localStorage
    localStorage.setItem('hubitat_token', token);
    localStorage.setItem('echo_device_id', deviceId);

    showNotification('Hubitat configuration saved!');
    updateHubitatStatus('configured');
}

async function testHubitatConnection() {
    const token = localStorage.getItem('hubitat_token');
    const deviceId = localStorage.getItem('echo_device_id');

    if (!token || !deviceId) {
        showNotification('Please configure Hubitat settings first', 'error');
        return;
    }

    try {
        updateHubitatStatus('testing');
        showNotification('Testing Hubitat connection...', 'info');

        const response = await fetch('/api/hubitat/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                token: token,
                device_id: deviceId
            })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            updateHubitatStatus('online');
            showNotification('Hubitat connection test successful!');
        } else {
            updateHubitatStatus('offline');
            showNotification('Hubitat connection failed: ' + (result.error || 'Unknown error'), 'error');
        }

    } catch (error) {
        updateHubitatStatus('offline');
        showNotification('Hubitat connection failed: ' + error.message, 'error');
    }
}

async function sendHubitatCommand(command) {
    const token = localStorage.getItem('hubitat_token');
    const deviceId = localStorage.getItem('echo_device_id');

    if (!token || !deviceId) {
        showNotification('Please configure Hubitat settings first', 'error');
        return;
    }

    try {
        showNotification(`Sending command via Hubitat: "${command}"`, 'info');

        const response = await fetch('/api/hubitat/speak', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                token: token,
                device_id: deviceId,
                command: command
            })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            showNotification(`✅ Command sent to Alexa via Hubitat: "${command}"`);

            // Update lock status if it's a lock command
            if (command.includes('lock') && !command.includes('is')) {
                setTimeout(() => {
                    if (command.includes('main door')) {
                        updateLockStatus('main_door', 'locked');
                    } else if (command.includes('back door')) {
                        updateLockStatus('back_door', 'locked');
                    } else if (command.includes('all doors')) {
                        updateLockStatus('main_door', 'locked');
                        updateLockStatus('back_door', 'locked');
                    }
                }, 3000);
            }
        } else {
            showNotification('Failed to send command: ' + (result.error || 'Unknown error'), 'error');
        }

    } catch (error) {
        showNotification('Failed to send Hubitat command: ' + error.message, 'error');
    }
}

function sendCustomCommand() {
    const command = document.getElementById('customCommand').value.trim();

    if (!command) {
        showNotification('Please enter a command', 'error');
        return;
    }

    sendHubitatCommand(command);
    document.getElementById('customCommand').value = '';
}

function updateHubitatStatus(status) {
    const statusElement = document.getElementById('hubitatStatus');
    if (statusElement) {
        statusElement.className = `status-dot ${status}`;

        if (status === 'testing') {
            statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        } else if (status === 'online') {
            statusElement.innerHTML = '<i class="fas fa-check"></i>';
        } else if (status === 'offline') {
            statusElement.innerHTML = '<i class="fas fa-times"></i>';
        } else if (status === 'configured') {
            statusElement.innerHTML = '<i class="fas fa-cog"></i>';
        } else {
            statusElement.innerHTML = '<i class="fas fa-question-circle"></i>';
        }
    }
}

function updateLockStatus(lockId, status) {
    const statusElement = document.getElementById(`${camelCase(lockId)}Status`);
    const stateElement = document.getElementById(`${camelCase(lockId)}State`);

    if (statusElement) {
        statusElement.className = `lock-status ${status}`;
        statusElement.innerHTML = status === 'locked' ? '<i class="fas fa-lock"></i>' : '<i class="fas fa-unlock"></i>';
    }

    if (stateElement) {
        stateElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
    }
}

// Load Hubitat configuration on page load
document.addEventListener('DOMContentLoaded', function() {
    const token = localStorage.getItem('hubitat_token');
    const deviceId = localStorage.getItem('echo_device_id');

    if (token) {
        document.getElementById('hubitatToken').value = token;
    }

    if (deviceId) {
        document.getElementById('echoDeviceId').value = deviceId;
    }

    if (token && deviceId) {
        updateHubitatStatus('configured');
    }
});

function refreshAll() {
    // Refresh all camera iframes
    const iframes = document.querySelectorAll('.camera-content iframe');
    iframes.forEach(iframe => {
        const src = iframe.src;
        iframe.src = '';
        setTimeout(() => {
            iframe.src = src;
        }, 100);
    });

    // Refresh lock status
    fetch('/api/locks')
        .then(response => response.json())
        .then(data => {
            // Update lock displays
            Object.keys(data.locks).forEach(lockId => {
                const lock = data.locks[lockId];
                const statusElement = document.getElementById(`${camelCase(lockId)}Status`);
                const stateElement = document.getElementById(`${camelCase(lockId)}State`);

                if (statusElement) {
                    statusElement.className = `lock-status ${lock.status}`;
                    statusElement.innerHTML = lock.status === 'locked' ? '<i class="fas fa-lock"></i>' :
                                            lock.status === 'unlocked' ? '<i class="fas fa-unlock"></i>' :
                                            '<i class="fas fa-question-circle"></i>';
                }

                if (stateElement) {
                    stateElement.textContent = lock.status.charAt(0).toUpperCase() + lock.status.slice(1);
                }
            });
        })
        .catch(error => console.error('Failed to refresh lock status:', error));

    // Show refresh notification
    showNotification('All cameras and locks refreshed');
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().catch(err => {
            console.error('Error attempting to enable fullscreen:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    const backgroundColor = type === 'error' ? 'rgba(244, 67, 54, 0.9)' : 'rgba(76, 175, 80, 0.9)';

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${backgroundColor};
        color: white;
        padding: 15px 25px;
        border-radius: 10px;
        z-index: 1002;
        font-weight: 500;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideIn 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, type === 'error' ? 5000 : 3000);
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new SecurityDashboard();
    loadSavedUrls();
});

function loadSavedUrls() {
    // Load portal URL
    const portalUrl = localStorage.getItem('wyzePortalUrl');
    if (portalUrl) {
        const portalContent = document.querySelector('.portal-content');
        portalContent.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; text-align: center; color: rgba(255,255,255,0.8); padding: 40px;">
                <i class="fas fa-external-link-alt" style="font-size: 3rem; margin-bottom: 20px; color: #64B5F6;"></i>
                <h3 style="margin-bottom: 15px;">Wyze Portal Ready</h3>
                <p style="margin-bottom: 25px; opacity: 0.9;">Click below to open your Wyze portal in a new tab</p>
                <button onclick="openWyzePortal()" class="config-btn" style="font-size: 1.1rem; padding: 15px 30px;">
                    <i class="fas fa-play"></i> Open Wyze Portal
                </button>
                <small style="margin-top: 15px; opacity: 0.6;">
                    Wyze prevents embedding for security. Opens in new tab.
                </small>
            </div>
        `;
    }

    // Load individual camera URLs
    const cameras = ['doorbell', 'front_yard', 'driveway'];
    cameras.forEach(cameraId => {
        const url = localStorage.getItem(`camera_${cameraId}_url`);
        if (url) {
            const cameraCard = document.querySelector(`[data-camera="${cameraId}"]`);
            const content = cameraCard.querySelector('.camera-content');
            content.innerHTML = `
                <iframe src="${url}"
                        frameborder="0"
                        allowfullscreen
                        sandbox="allow-same-origin allow-scripts allow-forms">
                </iframe>
                <div class="camera-overlay">
                    <button class="fullscreen-btn" onclick="openFullscreen('${cameraId}')">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            `;
        }
    });
}

// Handle modal clicks
window.onclick = function(event) {
    const modal = document.getElementById('fullscreenModal');
    if (event.target === modal) {
        closeFullscreen();
    }
}
'''

@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static assets"""
    if filename == 'style.css':
        return Response(DASHBOARD_CSS, mimetype='text/css')
    elif filename == 'script.js':
        return Response(DASHBOARD_JS, mimetype='application/javascript')
    return "File not found", 404

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({"error": "Resource not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {error}")
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # For local development
    port = int(os.environ.get('PORT', 8000))
    app.run(host='0.0.0.0', port=port, debug=True)
else:
    # For production (Azure App Service)
    application = app