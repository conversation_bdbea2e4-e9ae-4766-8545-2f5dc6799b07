#!/usr/bin/env python3
"""
Flask app for Camera Dashboard with proxy support for Azure deployment
"""

from flask import Flask, render_template, send_from_directory, request, Response
import requests
import os
from urllib.parse import urlparse, unquote

app = Flask(__name__)

# Configure for Azure App Service
app.config['DEBUG'] = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

@app.route('/')
def index():
    """Serve the main dashboard page"""
    return send_from_directory('.', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """Serve static files (CSS, JS, etc.)"""
    return send_from_directory('.', filename)

@app.route('/proxy/<path:url>')
def proxy(url):
    """Proxy requests to bypass CORS restrictions"""
    try:
        # Decode the URL
        target_url = unquote(url)
        
        # Validate URL
        parsed = urlparse(target_url)
        if not parsed.scheme or not parsed.netloc:
            return "Invalid URL", 400
        
        # Get request headers, excluding some that shouldn't be forwarded
        headers = {}
        for key, value in request.headers:
            if key.lower() not in ['host', 'connection', 'content-length']:
                headers[key] = value
        
        # Add user agent if not present
        if 'User-Agent' not in headers:
            headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        
        # Make the request
        response = requests.get(
            target_url,
            headers=headers,
            timeout=30,
            allow_redirects=True,
            stream=True
        )
        
        # Create response with appropriate headers
        def generate():
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    yield chunk
        
        # Filter response headers
        response_headers = {}
        for key, value in response.headers.items():
            if key.lower() not in ['x-frame-options', 'content-security-policy', 'content-encoding', 'transfer-encoding']:
                response_headers[key] = value
        
        # Add CORS headers
        response_headers['Access-Control-Allow-Origin'] = '*'
        response_headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response_headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        response_headers['X-Frame-Options'] = 'SAMEORIGIN'
        
        return Response(
            generate(),
            status=response.status_code,
            headers=response_headers
        )
        
    except requests.exceptions.Timeout:
        return "Request timeout", 504
    except requests.exceptions.RequestException as e:
        return f"Request failed: {str(e)}", 502
    except Exception as e:
        return f"Proxy error: {str(e)}", 500

@app.route('/proxy/<path:url>', methods=['OPTIONS'])
def proxy_options(url):
    """Handle CORS preflight requests"""
    response = Response()
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
    return response

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return "File not found", 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return "Internal server error", 500

# Health check endpoint for Azure
@app.route('/health')
def health():
    """Health check endpoint"""
    return {"status": "healthy", "service": "camera-dashboard"}, 200

if __name__ == '__main__':
    # For local development
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=True)
else:
    # For production (Azure App Service)
    application = app