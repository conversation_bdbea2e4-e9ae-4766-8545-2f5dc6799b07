#!/usr/bin/env python3
"""
Azure-optimized Security Camera Dashboard for Wyze Cameras
Designed specifically for Azure App Service with minimal dependencies
"""

from flask import Flask, render_template_string, jsonify, request, Response
import os
import json
import time
from datetime import datetime
import logging

# Configure logging for Azure
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Azure App Service configuration
app.config['DEBUG'] = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

# Default camera configuration for your security setup
DEFAULT_CAMERAS = {
    "doorbell": {
        "name": "Doorbell Camera",
        "description": "Front door security camera",
        "url": "",  # Will be configured via settings
        "rtmp_url": "",  # RTMP stream if available
        "type": "wyze",
        "position": 1,
        "active": True,
        "placeholder": "Configure your Wyze doorbell camera URL in settings"
    },
    "front_yard": {
        "name": "Front Yard Camera",
        "description": "Front yard security monitoring",
        "url": "",  # Will be configured via settings
        "rtmp_url": "",  # RTMP stream if available
        "type": "wyze",
        "position": 2,
        "active": True,
        "placeholder": "Configure your Wyze front yard camera URL in settings"
    },
    "driveway": {
        "name": "Driveway Camera",
        "description": "Driveway security monitoring",
        "url": "",  # Will be configured via settings
        "rtmp_url": "",  # RTMP stream if available
        "type": "wyze",
        "position": 3,
        "active": True,
        "placeholder": "Configure your Wyze driveway camera URL in settings"
    }
}

@app.route('/')
def dashboard():
    """Main security dashboard page"""
    return render_template_string(DASHBOARD_HTML)

@app.route('/api/cameras')
def get_cameras():
    """API endpoint to get camera configuration"""
    return jsonify({
        "cameras": DEFAULT_CAMERAS,
        "timestamp": datetime.now().isoformat(),
        "status": "online"
    })

@app.route('/api/cameras/<camera_id>/status')
def camera_status(camera_id):
    """Get status of a specific camera"""
    if camera_id not in DEFAULT_CAMERAS:
        return jsonify({"error": "Camera not found"}), 404

    camera = DEFAULT_CAMERAS[camera_id]
    return jsonify({
        "camera_id": camera_id,
        "name": camera["name"],
        "status": "online" if camera["active"] else "offline",
        "last_check": datetime.now().isoformat(),
        "type": camera["type"]
    })

@app.route('/api/dashboard/stats')
def dashboard_stats():
    """Get dashboard statistics"""
    active_cameras = sum(1 for cam in DEFAULT_CAMERAS.values() if cam["active"])
    return jsonify({
        "total_cameras": len(DEFAULT_CAMERAS),
        "active_cameras": active_cameras,
        "system_status": "operational",
        "last_update": datetime.now().isoformat()
    })

@app.route('/api/cameras/<camera_id>/configure', methods=['POST'])
def configure_camera(camera_id):
    """Configure a specific camera URL"""
    if camera_id not in DEFAULT_CAMERAS:
        return jsonify({"error": "Camera not found"}), 404

    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({"error": "URL is required"}), 400

    # Update camera configuration (in production, you'd save this to a database)
    DEFAULT_CAMERAS[camera_id]['url'] = data['url']
    if 'rtmp_url' in data:
        DEFAULT_CAMERAS[camera_id]['rtmp_url'] = data['rtmp_url']

    return jsonify({
        "message": f"Camera {camera_id} configured successfully",
        "camera": DEFAULT_CAMERAS[camera_id]
    })

@app.route('/settings')
def settings_page():
    """Camera settings configuration page"""
    return render_template_string(SETTINGS_HTML)

# Health check for Azure
@app.route('/health')
def health():
    """Azure health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "wyze-security-dashboard",
        "version": "2.0",
        "timestamp": datetime.now().isoformat()
    }), 200

# HTML Template for the dashboard
DASHBOARD_HTML = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wyze Security Dashboard</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-shield-alt"></i> Security Dashboard</h1>
                <div class="header-controls">
                    <a href="/settings" class="settings-btn">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                    <div class="status-indicator">
                        <span class="status-dot online"></span>
                        <span>System Online</span>
                    </div>
                </div>
            </div>
        </header>

        <div class="stats-bar">
            <div class="stat-item">
                <i class="fas fa-video"></i>
                <span class="stat-value" id="activeCameras">3</span>
                <span class="stat-label">Active Cameras</span>
            </div>
            <div class="stat-item">
                <i class="fas fa-clock"></i>
                <span class="stat-value" id="lastUpdate">--:--</span>
                <span class="stat-label">Last Update</span>
            </div>
            <div class="stat-item">
                <i class="fas fa-wifi"></i>
                <span class="stat-value">Online</span>
                <span class="stat-label">Connection</span>
            </div>
        </div>

        <div class="camera-grid">
            <div class="camera-card" data-camera="doorbell">
                <div class="camera-header">
                    <h3><i class="fas fa-door-open"></i> Doorbell Camera</h3>
                    <div class="camera-status online"></div>
                </div>
                <div class="camera-content">
                    <div class="camera-placeholder">
                        <i class="fas fa-video-slash"></i>
                        <h4>Camera Not Configured</h4>
                        <p>Configure your Wyze doorbell camera URL in settings</p>
                        <a href="/settings" class="config-btn">Configure Now</a>
                    </div>
                </div>
                <div class="camera-footer">
                    <span>Front Door Security</span>
                </div>
            </div>

            <div class="camera-card" data-camera="front_yard">
                <div class="camera-header">
                    <h3><i class="fas fa-tree"></i> Front Yard Camera</h3>
                    <div class="camera-status online"></div>
                </div>
                <div class="camera-content">
                    <div class="camera-placeholder">
                        <i class="fas fa-video-slash"></i>
                        <h4>Camera Not Configured</h4>
                        <p>Configure your Wyze front yard camera URL in settings</p>
                        <a href="/settings" class="config-btn">Configure Now</a>
                    </div>
                </div>
                <div class="camera-footer">
                    <span>Front Yard Monitoring</span>
                </div>
            </div>

            <div class="camera-card" data-camera="driveway">
                <div class="camera-header">
                    <h3><i class="fas fa-car"></i> Driveway Camera</h3>
                    <div class="camera-status online"></div>
                </div>
                <div class="camera-content">
                    <div class="camera-placeholder">
                        <i class="fas fa-video-slash"></i>
                        <h4>Camera Not Configured</h4>
                        <p>Configure your Wyze driveway camera URL in settings</p>
                        <a href="/settings" class="config-btn">Configure Now</a>
                    </div>
                </div>
                <div class="camera-footer">
                    <span>Driveway Security</span>
                </div>
            </div>
        </div>

        <div class="controls-panel">
            <button class="control-btn" onclick="refreshAll()">
                <i class="fas fa-sync-alt"></i> Refresh All
            </button>
            <button class="control-btn" onclick="toggleFullscreen()">
                <i class="fas fa-expand-arrows-alt"></i> Fullscreen
            </button>
        </div>
    </div>

    <!-- Fullscreen Modal -->
    <div id="fullscreenModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeFullscreen()">&times;</span>
            <div id="fullscreenContent"></div>
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
'''

# CSS Styles
DASHBOARD_CSS = '''
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
    color: #fff;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.settings-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 20px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 300;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: #4CAF50;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.stats-bar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-item i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #64B5F6;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.camera-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.camera-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.camera-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.camera-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.2);
}

.camera-header h3 {
    font-size: 1.3rem;
    font-weight: 500;
}

.camera-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

.camera-content {
    position: relative;
    height: 300px;
    background: #000;
}

.camera-content iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.camera-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    padding: 20px;
}

.camera-placeholder i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.5);
}

.camera-placeholder h4 {
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.camera-placeholder p {
    margin-bottom: 20px;
    font-size: 0.9rem;
    opacity: 0.8;
}

.config-btn {
    background: #2196F3;
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.config-btn:hover {
    background: #1976D2;
    transform: translateY(-2px);
}

.camera-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.camera-card:hover .camera-overlay {
    opacity: 1;
}

.fullscreen-btn {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: background 0.3s ease;
}

.fullscreen-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

.camera-footer {
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.1);
    text-align: center;
    font-size: 0.9rem;
    opacity: 0.8;
}

.controls-panel {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
}

.modal-content {
    position: relative;
    margin: 2% auto;
    width: 95%;
    height: 90%;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
}

.close {
    position: absolute;
    top: 15px;
    right: 25px;
    color: #fff;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
}

.close:hover {
    color: #f44336;
}

#fullscreenContent {
    width: 100%;
    height: 100%;
}

#fullscreenContent iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .camera-grid {
        grid-template-columns: 1fr;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-content h1 {
        font-size: 2rem;
    }

    .controls-panel {
        flex-direction: column;
        align-items: center;
    }
}
'''

# JavaScript for dashboard functionality
DASHBOARD_JS = '''
class SecurityDashboard {
    constructor() {
        this.cameras = {};
        this.updateInterval = null;
        this.init();
    }

    init() {
        this.loadCameraData();
        this.startAutoUpdate();
        this.updateTimestamp();

        // Update timestamp every minute
        setInterval(() => this.updateTimestamp(), 60000);
    }

    async loadCameraData() {
        try {
            const response = await fetch('/api/cameras');
            const data = await response.json();
            this.cameras = data.cameras;
            this.updateStats();
        } catch (error) {
            console.error('Failed to load camera data:', error);
        }
    }

    updateStats() {
        const activeCameras = Object.values(this.cameras).filter(cam => cam.active).length;
        document.getElementById('activeCameras').textContent = activeCameras;
    }

    updateTimestamp() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
        document.getElementById('lastUpdate').textContent = timeString;
    }

    startAutoUpdate() {
        // Check camera status every 30 seconds
        this.updateInterval = setInterval(async () => {
            await this.checkCameraStatus();
        }, 30000);
    }

    async checkCameraStatus() {
        for (const cameraId of Object.keys(this.cameras)) {
            try {
                const response = await fetch(`/api/cameras/${cameraId}/status`);
                const status = await response.json();
                this.updateCameraStatus(cameraId, status.status);
            } catch (error) {
                console.error(`Failed to check status for ${cameraId}:`, error);
                this.updateCameraStatus(cameraId, 'offline');
            }
        }
    }

    updateCameraStatus(cameraId, status) {
        const cameraCard = document.querySelector(`[data-camera="${cameraId}"]`);
        if (cameraCard) {
            const statusIndicator = cameraCard.querySelector('.camera-status');
            statusIndicator.className = `camera-status ${status}`;
        }
    }
}

// Global functions for UI interactions
function openFullscreen(cameraId) {
    const modal = document.getElementById('fullscreenModal');
    const content = document.getElementById('fullscreenContent');

    // Get the camera iframe source
    const cameraCard = document.querySelector(`[data-camera="${cameraId}"]`);
    const iframe = cameraCard.querySelector('iframe');

    content.innerHTML = `
        <iframe src="${iframe.src}"
                frameborder="0"
                allowfullscreen
                sandbox="allow-same-origin allow-scripts allow-forms">
        </iframe>
    `;

    modal.style.display = 'block';

    // Close on Escape key
    document.addEventListener('keydown', function escHandler(e) {
        if (e.key === 'Escape') {
            closeFullscreen();
            document.removeEventListener('keydown', escHandler);
        }
    });
}

function closeFullscreen() {
    const modal = document.getElementById('fullscreenModal');
    modal.style.display = 'none';
}

function refreshAll() {
    // Refresh all camera iframes
    const iframes = document.querySelectorAll('.camera-content iframe');
    iframes.forEach(iframe => {
        const src = iframe.src;
        iframe.src = '';
        setTimeout(() => {
            iframe.src = src;
        }, 100);
    });

    // Show refresh notification
    showNotification('All cameras refreshed');
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().catch(err => {
            console.error('Error attempting to enable fullscreen:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(76, 175, 80, 0.9);
        color: white;
        padding: 15px 25px;
        border-radius: 10px;
        z-index: 1002;
        font-weight: 500;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideIn 0.3s ease;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new SecurityDashboard();
});

// Handle modal clicks
window.onclick = function(event) {
    const modal = document.getElementById('fullscreenModal');
    if (event.target === modal) {
        closeFullscreen();
    }
}
'''

@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static assets"""
    if filename == 'style.css':
        return Response(DASHBOARD_CSS, mimetype='text/css')
    elif filename == 'script.js':
        return Response(DASHBOARD_JS, mimetype='application/javascript')
    return "File not found", 404

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({"error": "Resource not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {error}")
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # For local development
    port = int(os.environ.get('PORT', 8000))
    app.run(host='0.0.0.0', port=port, debug=True)
else:
    # For production (Azure App Service)
    application = app