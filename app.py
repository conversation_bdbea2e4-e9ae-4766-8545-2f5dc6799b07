#!/usr/bin/env python3
"""
Azure-optimized Security Camera Dashboard for Wyze Cameras
Designed specifically for Azure App Service with minimal dependencies
"""

from flask import Flask, render_template_string, jsonify, request, Response
import os
import json
import time
from datetime import datetime
import logging

# Configure logging for Azure
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Azure App Service configuration
app.config['DEBUG'] = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

# Default camera configuration for your security setup
DEFAULT_CAMERAS = {
    "doorbell": {
        "name": "Doorbell Camera",
        "description": "Front door security camera",
        "url": "",  # Will be configured via settings
        "rtmp_url": "",  # RTMP stream if available
        "type": "wyze",
        "position": 1,
        "active": True,
        "placeholder": "Configure your Wyze doorbell camera URL in settings"
    },
    "front_yard": {
        "name": "Front Yard Camera",
        "description": "Front yard security monitoring",
        "url": "",  # Will be configured via settings
        "rtmp_url": "",  # RTMP stream if available
        "type": "wyze",
        "position": 2,
        "active": True,
        "placeholder": "Configure your Wyze front yard camera URL in settings"
    },
    "driveway": {
        "name": "Driveway Camera",
        "description": "Driveway security monitoring",
        "url": "",  # Will be configured via settings
        "rtmp_url": "",  # RTMP stream if available
        "type": "wyze",
        "position": 3,
        "active": True,
        "placeholder": "Configure your Wyze driveway camera URL in settings"
    }
}

@app.route('/')
def dashboard():
    """Main security dashboard page"""
    return render_template_string(DASHBOARD_HTML)

@app.route('/api/cameras')
def get_cameras():
    """API endpoint to get camera configuration"""
    return jsonify({
        "cameras": DEFAULT_CAMERAS,
        "timestamp": datetime.now().isoformat(),
        "status": "online"
    })

@app.route('/api/cameras/<camera_id>/status')
def camera_status(camera_id):
    """Get status of a specific camera"""
    if camera_id not in DEFAULT_CAMERAS:
        return jsonify({"error": "Camera not found"}), 404

    camera = DEFAULT_CAMERAS[camera_id]
    return jsonify({
        "camera_id": camera_id,
        "name": camera["name"],
        "status": "online" if camera["active"] else "offline",
        "last_check": datetime.now().isoformat(),
        "type": camera["type"]
    })

@app.route('/api/dashboard/stats')
def dashboard_stats():
    """Get dashboard statistics"""
    active_cameras = sum(1 for cam in DEFAULT_CAMERAS.values() if cam["active"])
    return jsonify({
        "total_cameras": len(DEFAULT_CAMERAS),
        "active_cameras": active_cameras,
        "system_status": "operational",
        "last_update": datetime.now().isoformat()
    })

@app.route('/api/cameras/<camera_id>/configure', methods=['POST'])
def configure_camera(camera_id):
    """Configure a specific camera URL"""
    if camera_id not in DEFAULT_CAMERAS:
        return jsonify({"error": "Camera not found"}), 404

    data = request.get_json()
    if not data or 'url' not in data:
        return jsonify({"error": "URL is required"}), 400

    # Update camera configuration (in production, you'd save this to a database)
    DEFAULT_CAMERAS[camera_id]['url'] = data['url']
    if 'rtmp_url' in data:
        DEFAULT_CAMERAS[camera_id]['rtmp_url'] = data['rtmp_url']

    return jsonify({
        "message": f"Camera {camera_id} configured successfully",
        "camera": DEFAULT_CAMERAS[camera_id]
    })

# Settings page HTML
SETTINGS_HTML = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Settings - Wyze Security Dashboard</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-cog"></i> Camera Settings</h1>
                <a href="/" class="settings-btn">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </header>

        <div class="settings-content">
            <div class="settings-section">
                <h2><i class="fas fa-desktop"></i> Wyze Portal Configuration</h2>
                <p>Enter your Wyze portal URL here. The portal will open in a new window when you click "Portal View":</p>
                <div class="form-group">
                    <label for="portalUrl">Wyze Portal URL:</label>
                    <input type="url" id="portalUrl" placeholder="https://my.wyze.com/live or your portal URL">
                    <button onclick="savePortalUrl()" class="save-btn">Save Portal URL</button>
                </div>
                <div class="info-box">
                    <i class="fas fa-info-circle"></i>
                    <p><strong>How it works:</strong> Due to security restrictions, Wyze cannot be embedded directly. Instead, Portal View will open your Wyze portal in a new window/tab where you can view all your cameras.</p>
                </div>
                <div class="info-box" style="background: rgba(255, 193, 7, 0.2); border-color: rgba(255, 193, 7, 0.4);">
                    <i class="fas fa-lightbulb" style="color: #FFC107;"></i>
                    <p><strong>Tip:</strong> After saving your URL, go back to the dashboard, click "Portal View", then click "Open Wyze Portal" to launch your cameras in a new window.</p>
                </div>
            </div>

            <div class="settings-section">
                <h2><i class="fas fa-video"></i> Individual Camera Streams</h2>
                <p>If you have individual stream URLs for each camera, configure them here for Grid View:</p>

                <div class="camera-config">
                    <h3><i class="fas fa-door-open"></i> Doorbell Camera</h3>
                    <div class="form-group">
                        <label for="doorbellUrl">Stream URL:</label>
                        <input type="url" id="doorbellUrl" placeholder="Individual doorbell camera stream URL">
                        <button onclick="saveCameraUrl('doorbell')" class="save-btn">Save</button>
                    </div>
                </div>

                <div class="camera-config">
                    <h3><i class="fas fa-tree"></i> Front Yard Camera</h3>
                    <div class="form-group">
                        <label for="frontYardUrl">Stream URL:</label>
                        <input type="url" id="frontYardUrl" placeholder="Individual front yard camera stream URL">
                        <button onclick="saveCameraUrl('front_yard')" class="save-btn">Save</button>
                    </div>
                </div>

                <div class="camera-config">
                    <h3><i class="fas fa-car"></i> Driveway Camera</h3>
                    <div class="form-group">
                        <label for="drivewayUrl">Stream URL:</label>
                        <input type="url" id="drivewayUrl" placeholder="Individual driveway camera stream URL">
                        <button onclick="saveCameraUrl('driveway')" class="save-btn">Save</button>
                    </div>
                </div>
            </div>

            <div class="settings-section">
                <h2><i class="fas fa-question-circle"></i> How to Get Camera URLs</h2>
                <div class="help-content">
                    <h4>For Wyze Cameras:</h4>
                    <ul>
                        <li><strong>Portal View:</strong> Use your Wyze web portal URL (like https://my.wyze.com/live)</li>
                        <li><strong>Individual Streams:</strong> You may need to enable RTSP in the Wyze app for each camera</li>
                        <li><strong>RTSP Format:</strong> Usually rtsp://username:password@camera-ip:554/live</li>
                    </ul>

                    <h4>Alternative Options:</h4>
                    <ul>
                        <li>Use Wyze Bridge or similar tools to create RTSP streams</li>
                        <li>Check if your cameras support direct HTTP/MJPEG streams</li>
                        <li>Consider using Home Assistant or similar platforms</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function savePortalUrl() {
            const url = document.getElementById('portalUrl').value;
            if (!url) {
                alert('Please enter a portal URL');
                return;
            }

            // Save to localStorage for now (in production, you'd save to server)
            localStorage.setItem('wyzePortalUrl', url);
            alert('Portal URL saved! Go back to dashboard and try Portal View.');
        }

        function saveCameraUrl(cameraId) {
            const inputId = cameraId === 'front_yard' ? 'frontYardUrl' : cameraId + 'Url';
            const url = document.getElementById(inputId).value;

            if (!url) {
                alert('Please enter a camera URL');
                return;
            }

            // Save to localStorage for now
            localStorage.setItem(`camera_${cameraId}_url`, url);
            alert(`${cameraId} camera URL saved!`);
        }

        // Load saved URLs on page load
        document.addEventListener('DOMContentLoaded', function() {
            const portalUrl = localStorage.getItem('wyzePortalUrl');
            if (portalUrl) {
                document.getElementById('portalUrl').value = portalUrl;
            }

            const doorbellUrl = localStorage.getItem('camera_doorbell_url');
            if (doorbellUrl) {
                document.getElementById('doorbellUrl').value = doorbellUrl;
            }

            const frontYardUrl = localStorage.getItem('camera_front_yard_url');
            if (frontYardUrl) {
                document.getElementById('frontYardUrl').value = frontYardUrl;
            }

            const drivewayUrl = localStorage.getItem('camera_driveway_url');
            if (drivewayUrl) {
                document.getElementById('drivewayUrl').value = drivewayUrl;
            }
        });
    </script>
</body>
</html>
'''

@app.route('/settings')
def settings_page():
    """Camera settings configuration page"""
    return render_template_string(SETTINGS_HTML)

@app.route('/proxy')
def proxy_page():
    """Proxy page to load external URLs without iframe restrictions"""
    url = request.args.get('url')
    if not url:
        return "No URL provided", 400

    proxy_html = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Camera Feed</title>
        <style>
            body {{ margin: 0; padding: 0; }}
            .proxy-container {{
                width: 100vw;
                height: 100vh;
                display: flex;
                flex-direction: column;
            }}
            .proxy-header {{
                background: #1e3c72;
                color: white;
                padding: 10px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }}
            .proxy-content {{
                flex: 1;
                background: #000;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-family: Arial, sans-serif;
            }}
            .back-btn {{
                background: rgba(255,255,255,0.2);
                color: white;
                text-decoration: none;
                padding: 8px 16px;
                border-radius: 4px;
                border: 1px solid rgba(255,255,255,0.3);
            }}
            .back-btn:hover {{
                background: rgba(255,255,255,0.3);
            }}
        </style>
    </head>
    <body>
        <div class="proxy-container">
            <div class="proxy-header">
                <span>Camera Feed Proxy</span>
                <a href="/" class="back-btn">← Back to Dashboard</a>
            </div>
            <div class="proxy-content">
                <div style="text-align: center;">
                    <h2>🔗 External Link</h2>
                    <p>Click the link below to open your Wyze portal in a new tab:</p>
                    <a href="{url}" target="_blank" style="color: #64B5F6; font-size: 1.2rem; text-decoration: none;">
                        Open Wyze Portal →
                    </a>
                    <br><br>
                    <small style="opacity: 0.7;">
                        Note: Wyze prevents embedding for security reasons.<br>
                        We'll open it in a new tab instead.
                    </small>
                </div>
            </div>
        </div>
        <script>
            // Auto-open the link
            setTimeout(() => {{
                window.open('{url}', '_blank');
            }}, 1000);
        </script>
    </body>
    </html>
    '''
    return proxy_html

# Health check for Azure
@app.route('/health')
def health():
    """Azure health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "wyze-security-dashboard",
        "version": "2.0",
        "timestamp": datetime.now().isoformat()
    }), 200

# HTML Template for the dashboard
DASHBOARD_HTML = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wyze Security Dashboard</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <div class="header-content">
                <h1><i class="fas fa-shield-alt"></i> Security Dashboard</h1>
                <div class="header-controls">
                    <a href="/settings" class="settings-btn">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                    <div class="status-indicator">
                        <span class="status-dot online"></span>
                        <span>System Online</span>
                    </div>
                </div>
            </div>
        </header>

        <div class="stats-bar">
            <div class="stat-item">
                <i class="fas fa-video"></i>
                <span class="stat-value" id="activeCameras">3</span>
                <span class="stat-label">Active Cameras</span>
            </div>
            <div class="stat-item">
                <i class="fas fa-clock"></i>
                <span class="stat-value" id="lastUpdate">--:--</span>
                <span class="stat-label">Last Update</span>
            </div>
            <div class="stat-item">
                <i class="fas fa-wifi"></i>
                <span class="stat-value">Online</span>
                <span class="stat-label">Connection</span>
            </div>
        </div>

        <div class="view-toggle">
            <button class="toggle-btn active" onclick="showGridView()">
                <i class="fas fa-th"></i> Grid View
            </button>
            <button class="toggle-btn" onclick="showPortalView()">
                <i class="fas fa-desktop"></i> Portal View
            </button>
        </div>

        <!-- Grid View (Individual Cameras) -->
        <div class="camera-grid" id="gridView">
            <div class="camera-card" data-camera="doorbell">
                <div class="camera-header">
                    <h3><i class="fas fa-door-open"></i> Doorbell Camera</h3>
                    <div class="camera-status online"></div>
                </div>
                <div class="camera-content">
                    <div class="camera-placeholder">
                        <i class="fas fa-video-slash"></i>
                        <h4>Individual Camera Feed</h4>
                        <p>Configure individual doorbell camera stream URL</p>
                        <a href="/settings" class="config-btn">Configure Now</a>
                    </div>
                </div>
                <div class="camera-footer">
                    <span>Front Door Security</span>
                </div>
            </div>

            <div class="camera-card" data-camera="front_yard">
                <div class="camera-header">
                    <h3><i class="fas fa-tree"></i> Front Yard Camera</h3>
                    <div class="camera-status online"></div>
                </div>
                <div class="camera-content">
                    <div class="camera-placeholder">
                        <i class="fas fa-video-slash"></i>
                        <h4>Individual Camera Feed</h4>
                        <p>Configure individual front yard camera stream URL</p>
                        <a href="/settings" class="config-btn">Configure Now</a>
                    </div>
                </div>
                <div class="camera-footer">
                    <span>Front Yard Monitoring</span>
                </div>
            </div>

            <div class="camera-card" data-camera="driveway">
                <div class="camera-header">
                    <h3><i class="fas fa-car"></i> Driveway Camera</h3>
                    <div class="camera-status online"></div>
                </div>
                <div class="camera-content">
                    <div class="camera-placeholder">
                        <i class="fas fa-video-slash"></i>
                        <h4>Individual Camera Feed</h4>
                        <p>Configure individual driveway camera stream URL</p>
                        <a href="/settings" class="config-btn">Configure Now</a>
                    </div>
                </div>
                <div class="camera-footer">
                    <span>Driveway Security</span>
                </div>
            </div>
        </div>

        <!-- Portal View (Single Wyze Portal) -->
        <div class="portal-view" id="portalView" style="display: none;">
            <div class="portal-container">
                <div class="portal-header">
                    <h3><i class="fas fa-shield-alt"></i> Wyze Security Portal</h3>
                    <div class="portal-status">
                        <span class="status-dot online"></span>
                        <span>Connected</span>
                    </div>
                </div>
                <div class="portal-content">
                    <div class="portal-placeholder">
                        <i class="fas fa-globe"></i>
                        <h4>Wyze Portal Not Configured</h4>
                        <p>Configure your Wyze portal URL to view all cameras in one interface</p>
                        <a href="/settings" class="config-btn">Configure Portal URL</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="controls-panel">
            <button class="control-btn" onclick="refreshAll()">
                <i class="fas fa-sync-alt"></i> Refresh All
            </button>
            <button class="control-btn" onclick="toggleFullscreen()">
                <i class="fas fa-expand-arrows-alt"></i> Fullscreen
            </button>
        </div>
    </div>

    <!-- Fullscreen Modal -->
    <div id="fullscreenModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeFullscreen()">&times;</span>
            <div id="fullscreenContent"></div>
        </div>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
'''

# CSS Styles
DASHBOARD_CSS = '''
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    min-height: 100vh;
    color: #fff;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.dashboard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.settings-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 20px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 300;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: #4CAF50;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.stats-bar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-item i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #64B5F6;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.view-toggle {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
}

.toggle-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.toggle-btn.active {
    background: #2196F3;
    border-color: #2196F3;
}

.portal-view {
    margin-bottom: 30px;
}

.portal-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: 600px;
}

.portal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
}

.portal-header h3 {
    font-size: 1.5rem;
    font-weight: 500;
}

.portal-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.portal-content {
    height: calc(100% - 80px);
    background: #000;
    position: relative;
}

.portal-content iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.portal-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    padding: 40px;
}

.portal-placeholder i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.5);
}

.portal-placeholder h4 {
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.portal-placeholder p {
    margin-bottom: 25px;
    font-size: 1rem;
    opacity: 0.8;
    max-width: 400px;
}

.camera-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.camera-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.camera-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.camera-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.2);
}

.camera-header h3 {
    font-size: 1.3rem;
    font-weight: 500;
}

.camera-status {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

.camera-content {
    position: relative;
    height: 300px;
    background: #000;
}

.camera-content iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.camera-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    padding: 20px;
}

.camera-placeholder i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.5);
}

.camera-placeholder h4 {
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.camera-placeholder p {
    margin-bottom: 20px;
    font-size: 0.9rem;
    opacity: 0.8;
}

.config-btn {
    background: #2196F3;
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 20px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.config-btn:hover {
    background: #1976D2;
    transform: translateY(-2px);
}

.camera-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.camera-card:hover .camera-overlay {
    opacity: 1;
}

.fullscreen-btn {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: background 0.3s ease;
}

.fullscreen-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

.camera-footer {
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.1);
    text-align: center;
    font-size: 0.9rem;
    opacity: 0.8;
}

.controls-panel {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
}

.modal-content {
    position: relative;
    margin: 2% auto;
    width: 95%;
    height: 90%;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
}

.close {
    position: absolute;
    top: 15px;
    right: 25px;
    color: #fff;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
}

.close:hover {
    color: #f44336;
}

#fullscreenContent {
    width: 100%;
    height: 100%;
}

#fullscreenContent iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Settings Page Styles */
.settings-content {
    max-width: 800px;
    margin: 0 auto;
}

.settings-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-section h2 {
    margin-bottom: 15px;
    font-size: 1.5rem;
    color: #64B5F6;
}

.settings-section p {
    margin-bottom: 20px;
    opacity: 0.9;
    line-height: 1.6;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    margin-bottom: 10px;
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.save-btn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.save-btn:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.camera-config {
    background: rgba(0, 0, 0, 0.2);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.camera-config h3 {
    margin-bottom: 15px;
    color: #81C784;
}

.info-box {
    background: rgba(33, 150, 243, 0.2);
    border: 1px solid rgba(33, 150, 243, 0.4);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.info-box i {
    color: #2196F3;
    font-size: 1.2rem;
    margin-top: 2px;
}

.help-content h4 {
    color: #FFB74D;
    margin: 20px 0 10px 0;
}

.help-content ul {
    margin-left: 20px;
    margin-bottom: 20px;
}

.help-content li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .camera-grid {
        grid-template-columns: 1fr;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-content h1 {
        font-size: 2rem;
    }

    .controls-panel {
        flex-direction: column;
        align-items: center;
    }

    .view-toggle {
        flex-direction: column;
        align-items: center;
    }

    .settings-section {
        padding: 20px;
    }
}
'''

# JavaScript for dashboard functionality
DASHBOARD_JS = '''
class SecurityDashboard {
    constructor() {
        this.cameras = {};
        this.updateInterval = null;
        this.init();
    }

    init() {
        this.loadCameraData();
        this.startAutoUpdate();
        this.updateTimestamp();

        // Update timestamp every minute
        setInterval(() => this.updateTimestamp(), 60000);
    }

    async loadCameraData() {
        try {
            const response = await fetch('/api/cameras');
            const data = await response.json();
            this.cameras = data.cameras;
            this.updateStats();
        } catch (error) {
            console.error('Failed to load camera data:', error);
        }
    }

    updateStats() {
        const activeCameras = Object.values(this.cameras).filter(cam => cam.active).length;
        document.getElementById('activeCameras').textContent = activeCameras;
    }

    updateTimestamp() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
        document.getElementById('lastUpdate').textContent = timeString;
    }

    startAutoUpdate() {
        // Check camera status every 30 seconds
        this.updateInterval = setInterval(async () => {
            await this.checkCameraStatus();
        }, 30000);
    }

    async checkCameraStatus() {
        for (const cameraId of Object.keys(this.cameras)) {
            try {
                const response = await fetch(`/api/cameras/${cameraId}/status`);
                const status = await response.json();
                this.updateCameraStatus(cameraId, status.status);
            } catch (error) {
                console.error(`Failed to check status for ${cameraId}:`, error);
                this.updateCameraStatus(cameraId, 'offline');
            }
        }
    }

    updateCameraStatus(cameraId, status) {
        const cameraCard = document.querySelector(`[data-camera="${cameraId}"]`);
        if (cameraCard) {
            const statusIndicator = cameraCard.querySelector('.camera-status');
            statusIndicator.className = `camera-status ${status}`;
        }
    }
}

// Global functions for UI interactions
function showGridView() {
    console.log('Switching to Grid View');
    const gridView = document.getElementById('gridView');
    const portalView = document.getElementById('portalView');

    if (gridView) gridView.style.display = 'grid';
    if (portalView) portalView.style.display = 'none';

    // Update toggle buttons
    document.querySelectorAll('.toggle-btn').forEach(btn => btn.classList.remove('active'));
    const gridBtn = document.querySelector('.toggle-btn');
    if (gridBtn) gridBtn.classList.add('active');

    showNotification('Switched to Grid View');
}

function showPortalView() {
    console.log('Switching to Portal View');
    const gridView = document.getElementById('gridView');
    const portalView = document.getElementById('portalView');

    if (gridView) gridView.style.display = 'none';
    if (portalView) portalView.style.display = 'block';

    // Update toggle buttons
    document.querySelectorAll('.toggle-btn').forEach(btn => btn.classList.remove('active'));
    const portalBtn = document.querySelectorAll('.toggle-btn')[1];
    if (portalBtn) portalBtn.classList.add('active');

    showNotification('Switched to Portal View');
}

function openWyzePortal() {
    const portalUrl = localStorage.getItem('wyzePortalUrl');
    if (portalUrl) {
        window.open(portalUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        showNotification('Opening Wyze portal in new window...');
    } else {
        alert('No portal URL configured. Please go to Settings first.');
    }
}

function openFullscreen(cameraId) {
    const modal = document.getElementById('fullscreenModal');
    const content = document.getElementById('fullscreenContent');

    // Get the camera iframe source
    const cameraCard = document.querySelector(`[data-camera="${cameraId}"]`);
    const iframe = cameraCard.querySelector('iframe');

    content.innerHTML = `
        <iframe src="${iframe.src}"
                frameborder="0"
                allowfullscreen
                sandbox="allow-same-origin allow-scripts allow-forms">
        </iframe>
    `;

    modal.style.display = 'block';

    // Close on Escape key
    document.addEventListener('keydown', function escHandler(e) {
        if (e.key === 'Escape') {
            closeFullscreen();
            document.removeEventListener('keydown', escHandler);
        }
    });
}

function closeFullscreen() {
    const modal = document.getElementById('fullscreenModal');
    modal.style.display = 'none';
}

function refreshAll() {
    // Refresh all camera iframes
    const iframes = document.querySelectorAll('.camera-content iframe');
    iframes.forEach(iframe => {
        const src = iframe.src;
        iframe.src = '';
        setTimeout(() => {
            iframe.src = src;
        }, 100);
    });

    // Show refresh notification
    showNotification('All cameras refreshed');
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().catch(err => {
            console.error('Error attempting to enable fullscreen:', err);
        });
    } else {
        document.exitFullscreen();
    }
}

function showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(76, 175, 80, 0.9);
        color: white;
        padding: 15px 25px;
        border-radius: 10px;
        z-index: 1002;
        font-weight: 500;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideIn 0.3s ease;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new SecurityDashboard();
    loadSavedUrls();
});

function loadSavedUrls() {
    // Load portal URL
    const portalUrl = localStorage.getItem('wyzePortalUrl');
    if (portalUrl) {
        const portalContent = document.querySelector('.portal-content');
        portalContent.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; text-align: center; color: rgba(255,255,255,0.8); padding: 40px;">
                <i class="fas fa-external-link-alt" style="font-size: 3rem; margin-bottom: 20px; color: #64B5F6;"></i>
                <h3 style="margin-bottom: 15px;">Wyze Portal Ready</h3>
                <p style="margin-bottom: 25px; opacity: 0.9;">Click below to open your Wyze portal in a new tab</p>
                <button onclick="openWyzePortal()" class="config-btn" style="font-size: 1.1rem; padding: 15px 30px;">
                    <i class="fas fa-play"></i> Open Wyze Portal
                </button>
                <small style="margin-top: 15px; opacity: 0.6;">
                    Wyze prevents embedding for security. Opens in new tab.
                </small>
            </div>
        `;
    }

    // Load individual camera URLs
    const cameras = ['doorbell', 'front_yard', 'driveway'];
    cameras.forEach(cameraId => {
        const url = localStorage.getItem(`camera_${cameraId}_url`);
        if (url) {
            const cameraCard = document.querySelector(`[data-camera="${cameraId}"]`);
            const content = cameraCard.querySelector('.camera-content');
            content.innerHTML = `
                <iframe src="${url}"
                        frameborder="0"
                        allowfullscreen
                        sandbox="allow-same-origin allow-scripts allow-forms">
                </iframe>
                <div class="camera-overlay">
                    <button class="fullscreen-btn" onclick="openFullscreen('${cameraId}')">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            `;
        }
    });
}

// Handle modal clicks
window.onclick = function(event) {
    const modal = document.getElementById('fullscreenModal');
    if (event.target === modal) {
        closeFullscreen();
    }
}
'''

@app.route('/static/<path:filename>')
def static_files(filename):
    """Serve static assets"""
    if filename == 'style.css':
        return Response(DASHBOARD_CSS, mimetype='text/css')
    elif filename == 'script.js':
        return Response(DASHBOARD_JS, mimetype='application/javascript')
    return "File not found", 404

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({"error": "Resource not found"}), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    logger.error(f"Internal server error: {error}")
    return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    # For local development
    port = int(os.environ.get('PORT', 8000))
    app.run(host='0.0.0.0', port=port, debug=True)
else:
    # For production (Azure App Service)
    application = app