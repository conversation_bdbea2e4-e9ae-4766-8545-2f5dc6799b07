* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
}

.dashboard-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.dashboard-header {
    background-color: #2d2d2d;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #404040;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.dashboard-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #4CAF50;
}

.controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    background-color: #404040;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.control-btn:hover {
    background-color: #555555;
}

.camera-grid {
    flex: 1;
    display: grid;
    gap: 10px;
    padding: 10px;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    background-color: #1a1a1a;
}

.camera-grid.layout-3x2 {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
}

.camera-grid.layout-3x3 {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
}

.camera-grid.layout-4x3 {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 1fr);
}

.camera-panel {
    background-color: #2d2d2d;
    border-radius: 8px;
    border: 2px solid #404040;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.camera-panel:hover {
    transform: scale(1.02);
    box-shadow: 0 5px 20px rgba(76, 175, 80, 0.3);
    border-color: #4CAF50;
}

.camera-header {
    background-color: #404040;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.camera-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #f44336;
}

.camera-status.online {
    background-color: #4CAF50;
}

.camera-content {
    flex: 1;
    position: relative;
    background-color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
}

.camera-iframe {
    width: 100%;
    height: 100%;
    border: none;
    background-color: #000;
}

.camera-placeholder {
    color: #666;
    font-size: 16px;
    text-align: center;
}

.camera-error {
    color: #f44336;
    font-size: 14px;
    text-align: center;
    padding: 20px;
}

.camera-proxy {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #000;
}

.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #4CAF50;
    font-size: 14px;
}

.loading-spinner::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #4CAF50;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
}

.modal-content {
    background-color: #2d2d2d;
    margin: 5% auto;
    padding: 20px;
    border-radius: 10px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #fff;
}

.settings-form {
    margin-top: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #555;
    border-radius: 4px;
    background-color: #404040;
    color: white;
    font-size: 14px;
}

.camera-inputs {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 10px;
    margin: 15px 0;
}

.camera-input-group {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #404040;
    border-radius: 4px;
}

.camera-input-group label {
    font-size: 12px;
    color: #ccc;
}

.save-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    margin-top: 10px;
}

.save-btn:hover {
    background-color: #45a049;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 10px 15px;
    }
    
    .dashboard-header h1 {
        font-size: 20px;
    }
    
    .camera-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(4, 1fr);
        gap: 5px;
        padding: 5px;
    }
    
    .camera-grid.layout-3x2,
    .camera-grid.layout-3x3,
    .camera-grid.layout-4x3 {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}

/* Fullscreen styles */
.dashboard-container.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
}

.dashboard-container.fullscreen .dashboard-header {
    display: none;
}

.dashboard-container.fullscreen .camera-grid {
    padding: 5px;
    gap: 5px;
}