#!/usr/bin/env python3
"""
Simple HTTP server for camera dashboard
"""

import http.server
import socketserver
import os

class CustomHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('X-Frame-Options', 'SAMEORIGIN')
        super().end_headers()

def main():
    PORT = 8080
    
    with socketserver.TCPServer(("", PORT), CustomHandler) as httpd:
        print(f"Camera Dashboard running at http://localhost:{PORT}")
        print("Press Ctrl+C to stop")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()