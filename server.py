#!/usr/bin/env python3
"""
Proxy server for camera dashboard to handle CORS and iframe restrictions
"""

import http.server
import socketserver
import urllib.request
import urllib.parse
import json
import os
from urllib.error import URLError, HTTPError

class CameraProxyHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def do_GET(self):
        # Handle proxy requests
        if self.path.startswith('/proxy/'):
            self.handle_proxy_request()
        else:
            # Serve static files normally
            super().do_GET()
    
    def do_POST(self):
        if self.path.startswith('/proxy/'):
            self.handle_proxy_request()
        else:
            super().do_POST()
    
    def add_cors_headers(self):
        """Add CORS headers to allow cross-origin requests"""
        pass  # Will be added in end_headers
    
    def handle_proxy_request(self):
        """Handle proxy requests to bypass CORS restrictions"""
        try:
            # Extract the target URL from the proxy path
            # Format: /proxy/https://example.com/path
            proxy_path = self.path[7:]  # Remove '/proxy/'
            
            if not proxy_path:
                self.send_error(400, "No target URL provided")
                return
            
            # Decode URL if it's encoded
            target_url = urllib.parse.unquote(proxy_path)
            
            # Create request
            req = urllib.request.Request(target_url)
            
            # Copy headers from original request (except host)
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection']:
                    req.add_header(header, value)
            
            # Add user agent to look like a regular browser
            req.add_header('User-Agent', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
            
            # Make the request
            with urllib.request.urlopen(req, timeout=30) as response:
                # Send response headers
                self.send_response(200)
                
                # Copy response headers with CORS enabled
                for header, value in response.headers.items():
                    if header.lower() not in ['x-frame-options', 'content-security-policy']:
                        self.send_header(header, value)
                
                self.add_cors_headers()
                self.end_headers()
                
                # Stream the response data
                while True:
                    chunk = response.read(8192)
                    if not chunk:
                        break
                    self.wfile.write(chunk)
        
        except HTTPError as e:
            self.send_error(e.code, f"Proxy error: {e.reason}")
        except URLError as e:
            self.send_error(502, f"Proxy error: {e.reason}")
        except Exception as e:
            self.send_error(500, f"Proxy error: {str(e)}")
    
    def do_OPTIONS(self):
        """Handle preflight OPTIONS requests for CORS"""
        self.send_response(200)
        self.add_cors_headers()
        self.end_headers()
    
    def end_headers(self):
        # Add CORS headers for all responses
        if not hasattr(self, '_headers_sent'):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
            self.send_header('X-Frame-Options', 'SAMEORIGIN')
        super().end_headers()

def main():
    PORT = 8080
    Handler = CameraProxyHandler
    
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"Camera Dashboard Server running at http://localhost:{PORT}")
        print(f"Proxy endpoint: http://localhost:{PORT}/proxy/[URL]")
        print("Press Ctrl+C to stop the server")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()