# Security Camera Dashboard

A responsive web dashboard for viewing multiple security cameras in a grid layout. Designed specifically for Wyze cameras but supports multiple camera types and streaming protocols.

## Features

- **Multiple Grid Layouts**: 2x2, 3x2, 3x3, and 4x3 camera arrangements
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Multiple Camera Types**: 
  - Web page embedding (iframe) for Wyze web view
  - Image/MJPEG streams
  - RTSP streams (limited browser support)
- **Fullscreen Mode**: Both dashboard and individual camera fullscreen
- **Auto-refresh**: Configurable refresh intervals for image cameras
- **Local Storage**: Saves your camera configurations locally
- **Keyboard Shortcuts**: F11 for fullscreen, Escape to exit

## Setup Instructions

### For Wyze Cameras

1. **Web View Method** (Recommended):
   - Log into your Wyze account on the web
   - Navigate to your camera's live view
   - Copy the URL from your browser
   - Use this URL in the dashboard settings as an "iframe" type

2. **RTSP Method** (Requires Wyze Cam Plus):
   - Enable RTSP in your Wyze app for each camera
   - Use the provided RTSP URL in the dashboard
   - Note: Browser support for RTSP is limited

### Configuration

1. Open `index.html` in your web browser
2. Click the "Settings" button (⚙️)
3. Configure your cameras:
   - **Name**: Give each camera a descriptive name
   - **URL**: Enter the camera's web view URL or stream URL
   - **Type**: Choose the appropriate type:
     - `Web Page (iframe)`: For Wyze web view URLs
     - `Image/MJPEG`: For direct image streams
     - `RTSP Stream`: For RTSP video streams

4. Select your preferred grid layout
5. Set refresh interval (for image-type cameras)
6. Click "Save Settings"

## Usage

- **View Cameras**: All configured cameras display in the grid
- **Fullscreen Dashboard**: Click the fullscreen button or press F11
- **Individual Camera Fullscreen**: Double-click any camera panel
- **Settings**: Click the settings button to modify configuration
- **Status Indicators**: Green dot = online, Red dot = offline/error

## File Structure

```
dashboard/
├── index.html      # Main dashboard page
├── style.css       # Styling and responsive design
├── script.js       # Dashboard functionality
└── README.md       # This file
```

## Browser Compatibility

- **Chrome/Edge**: Full support for all features
- **Firefox**: Full support for all features
- **Safari**: Limited RTSP support
- **Mobile browsers**: Responsive design, limited RTSP support

## Troubleshooting

### Camera Not Loading
- Check if the camera URL is accessible
- Ensure CORS policies allow embedding (for iframe type)
- Try different camera types (iframe vs image vs RTSP)

### Wyze Specific Issues
- Wyze web view URLs may change periodically
- Some Wyze cameras require authentication cookies
- RTSP requires Wyze Cam Plus subscription

### RTSP Streams
- Most browsers don't natively support RTSP
- Consider using WebRTC or HLS alternatives
- Use VLC or other media players for RTSP testing

## Security Notes

- Dashboard runs entirely in your browser (client-side)
- Camera URLs are stored in browser's local storage
- No data is sent to external servers
- Consider using HTTPS for production deployments

## Customization

The dashboard is fully customizable:
- Modify `style.css` for different themes
- Edit `script.js` for additional functionality
- Add more camera types or streaming protocols
- Integrate with home automation systems

## Future Enhancements

- WebRTC support for better streaming
- Camera recording capabilities
- Motion detection alerts
- Integration with home automation platforms
- Mobile app version