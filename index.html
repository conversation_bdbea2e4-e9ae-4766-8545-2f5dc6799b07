<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Camera Dashboard</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="dashboard-container">
        <header class="dashboard-header">
            <h1>Security Camera Dashboard</h1>
            <div class="controls">
                <button id="fullscreenBtn" class="control-btn">⛶ Fullscreen</button>
                <button id="settingsBtn" class="control-btn">⚙️ Settings</button>
            </div>
        </header>

        <div class="camera-grid" id="cameraGrid">
            <!-- Camera panels will be dynamically generated -->
        </div>

        <!-- Settings Modal -->
        <div id="settingsModal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2>Camera Settings</h2>
                <div class="settings-form">
                    <div class="form-group">
                        <label for="gridLayout">Grid Layout:</label>
                        <select id="gridLayout">
                            <option value="2x2">2x2 (4 cameras)</option>
                            <option value="3x2">3x2 (6 cameras)</option>
                            <option value="3x3">3x3 (9 cameras)</option>
                            <option value="4x3">4x3 (12 cameras)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="refreshInterval">Refresh Interval (seconds):</label>
                        <input type="number" id="refreshInterval" min="5" max="300" value="30">
                    </div>
                    <div class="camera-inputs" id="cameraInputs">
                        <!-- Camera URL inputs will be generated dynamically -->
                    </div>
                    <button id="saveSettings" class="save-btn">Save Settings</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>